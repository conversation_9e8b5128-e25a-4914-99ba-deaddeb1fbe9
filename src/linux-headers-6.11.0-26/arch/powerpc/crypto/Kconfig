# SPDX-License-Identifier: GPL-2.0

menu "Accelerated Cryptographic Algorithms for CPU (powerpc)"

config CRYPTO_CURVE25519_PPC64
	tristate "Public key crypto: Curve25519 (PowerPC64)"
	depends on PPC64 && CPU_LITTLE_ENDIAN
	select CRYPTO_LIB_CURVE25519_GENERIC
	select CRYPTO_ARCH_HAVE_LIB_CURVE25519
	help
	  Curve25519 algorithm

	  Architecture: PowerPC64
	  - Little-endian

config CRYPTO_CRC32C_VPMSUM
	tristate "CRC32c"
	depends on PPC64 && ALTIVEC
	select CRYPTO_HASH
	select CRC32
	help
	  CRC32c CRC algorithm with the iSCSI polynomial (RFC 3385 and RFC 3720)

	  Architecture: powerpc64 using
	  - AltiVec extensions

	  Enable on POWER8 and newer processors for improved performance.

config CRYPTO_CRCT10DIF_VPMSUM
	tristate "CRC32T10DIF"
	depends on PPC64 && ALTIVEC && CRC_T10DIF
	select CRYPTO_HASH
	help
	  CRC16 CRC algorithm used for the T10 (SCSI) Data Integrity Field (DIF)

	  Architecture: powerpc64 using
	  - AltiVec extensions

	  Enable on POWER8 and newer processors for improved performance.

config CRYPTO_VPMSUM_TESTER
	tristate "CRC32c and CRC32T10DIF hardware acceleration tester"
	depends on CRYPTO_CRCT10DIF_VPMSUM && CRYPTO_CRC32C_VPMSUM
	help
	  Stress test for CRC32c and CRCT10DIF algorithms implemented with
	  powerpc64 AltiVec extensions (POWER8 vpmsum instructions).
	  Unless you are testing these algorithms, you don't need this.

config CRYPTO_MD5_PPC
	tristate "Digests: MD5"
	depends on PPC
	select CRYPTO_HASH
	help
	  MD5 message digest algorithm (RFC1321)

	  Architecture: powerpc

config CRYPTO_SHA1_PPC
	tristate "Hash functions: SHA-1"
	depends on PPC
	help
	  SHA-1 secure hash algorithm (FIPS 180)

	  Architecture: powerpc

config CRYPTO_SHA1_PPC_SPE
	tristate "Hash functions: SHA-1 (SPE)"
	depends on PPC && SPE
	help
	  SHA-1 secure hash algorithm (FIPS 180)

	  Architecture: powerpc using
	  - SPE (Signal Processing Engine) extensions

config CRYPTO_SHA256_PPC_SPE
	tristate "Hash functions: SHA-224 and SHA-256 (SPE)"
	depends on PPC && SPE
	select CRYPTO_SHA256
	select CRYPTO_HASH
	help
	  SHA-224 and SHA-256 secure hash algorithms (FIPS 180)

	  Architecture: powerpc using
	  - SPE (Signal Processing Engine) extensions

config CRYPTO_AES_PPC_SPE
	tristate "Ciphers: AES, modes: ECB/CBC/CTR/XTS (SPE)"
	depends on PPC && SPE
	select CRYPTO_SKCIPHER
	help
	  Block ciphers: AES cipher algorithms (FIPS-197)
	  Length-preserving ciphers: AES with ECB, CBC, CTR, and XTS modes

	  Architecture: powerpc using:
	  - SPE (Signal Processing Engine) extensions

	  SPE is available for:
	  - Processor Type: Freescale 8500
	  - CPU selection: e500 (8540)

	  This module should only be used for low power (router) devices
	  without hardware AES acceleration (e.g. caam crypto). It reduces the
	  size of the AES tables from 16KB to 8KB + 256 bytes and mitigates
	  timining attacks. Nevertheless it might be not as secure as other
	  architecture specific assembler implementations that work on 1KB
	  tables or 256 bytes S-boxes.

config CRYPTO_AES_GCM_P10
	tristate "Stitched AES/GCM acceleration support on P10 or later CPU (PPC)"
	depends on BROKEN
	depends on PPC64 && CPU_LITTLE_ENDIAN && VSX
	select CRYPTO_LIB_AES
	select CRYPTO_ALGAPI
	select CRYPTO_AEAD
	select CRYPTO_SKCIPHER
	help
	  AEAD cipher: AES cipher algorithms (FIPS-197)
	  GCM (Galois/Counter Mode) authenticated encryption mode (NIST SP800-38D)
	  Architecture: powerpc64 using:
	    - little-endian
	    - Power10 or later features

	  Support for cryptographic acceleration instructions on Power10 or
	  later CPU. This module supports stitched acceleration for AES/GCM.

config CRYPTO_CHACHA20_P10
	tristate "Ciphers: ChaCha20, XChacha20, XChacha12 (P10 or later)"
	depends on PPC64 && CPU_LITTLE_ENDIAN && VSX
	select CRYPTO_SKCIPHER
	select CRYPTO_LIB_CHACHA_GENERIC
	select CRYPTO_ARCH_HAVE_LIB_CHACHA
	help
	  Length-preserving ciphers: ChaCha20, XChaCha20, and XChaCha12
	  stream cipher algorithms

	  Architecture: PowerPC64
	  - Power10 or later
	  - Little-endian

config CRYPTO_POLY1305_P10
	tristate "Hash functions: Poly1305 (P10 or later)"
	depends on PPC64 && CPU_LITTLE_ENDIAN && VSX
	select CRYPTO_HASH
	select CRYPTO_LIB_POLY1305_GENERIC
	help
	  Poly1305 authenticator algorithm (RFC7539)

	  Architecture: PowerPC64
	  - Power10 or later
	  - Little-endian

config CRYPTO_DEV_VMX
        bool "Support for VMX cryptographic acceleration instructions"
        depends on PPC64 && VSX
        help
          Support for VMX cryptographic acceleration instructions.

config CRYPTO_DEV_VMX_ENCRYPT
	tristate "Encryption acceleration support on P8 CPU"
	depends on CRYPTO_DEV_VMX
	select CRYPTO_AES
	select CRYPTO_CBC
	select CRYPTO_CTR
	select CRYPTO_GHASH
	select CRYPTO_XTS
	default m
	help
	  Support for VMX cryptographic acceleration instructions on Power8 CPU.
	  This module supports acceleration for AES and GHASH in hardware. If you
	  choose 'M' here, this module will be called vmx-crypto.

endmenu
