# SPDX-License-Identifier: GPL-2.0
#
# KVM configuration
#

source "virt/kvm/Kconfig"

menuconfig VIRTUALIZATION
	bool "Virtualization"
	help
	  Say <PERSON> here to get to see options for using your Linux host to run
	  other operating systems inside virtual machines (guests).
	  This option alone does not add any kernel code.

	  If you say N, all options in this submenu will be skipped and
	  disabled.

if VIRTUALIZATION

config KVM
	bool
	select KVM_COMMON
	select HAVE_KVM_VCPU_ASYNC_I<PERSON><PERSON>
	select KVM_<PERSON>FIO
	select HAVE_KVM_IRQ_BYPASS

config KVM_BOOK3S_HANDLER
	bool

config KVM_BOOK3S_32_HANDLER
	bool
	select KVM_BOOK3S_HANDLER
	select KVM_MMIO

config KVM_BOOK3S_64_HANDLER
	bool
	select KVM_BOOK3S_HANDLER

config KVM_BOOK3S_PR_POSSIBLE
	bool
	select KVM_MMIO
	select KVM_GENERIC_MMU_NOTIFIER

config KVM_BOOK3S_HV_POSSIBLE
	bool

config KVM_BOOK3S_32
	tristate "KVM support for PowerPC book3s_32 processors"
	depends on PPC_BOOK3S_32 && !SMP && !PTE_64BIT
	depends on !CONTEXT_TRACKING_USER
	select KVM
	select KVM_BOOK3S_32_HANDLER
	select KVM_BOOK3S_PR_POSSIBLE
	select PPC_FPU
	help
	  Support running unmodified book3s_32 guest kernels
	  in virtual machines on book3s_32 host processors.

	  This module provides access to the hardware capabilities through
	  a character device node named /dev/kvm.

	  If unsure, say N.

config KVM_BOOK3S_64
	tristate "KVM support for PowerPC book3s_64 processors"
	depends on PPC_BOOK3S_64
	select KVM_BOOK3S_64_HANDLER
	select KVM
	select KVM_BOOK3S_PR_POSSIBLE if !KVM_BOOK3S_HV_POSSIBLE
	select PPC_64S_HASH_MMU
	select SPAPR_TCE_IOMMU if IOMMU_SUPPORT && (PPC_PSERIES || PPC_POWERNV)
	help
	  Support running unmodified book3s_64 and book3s_32 guest kernels
	  in virtual machines on book3s_64 host processors.

	  This module provides access to the hardware capabilities through
	  a character device node named /dev/kvm.

	  If unsure, say N.

config KVM_BOOK3S_64_HV
	tristate "KVM for POWER7 and later using hypervisor mode in host"
	depends on KVM_BOOK3S_64 && PPC_POWERNV
	select KVM_BOOK3S_HV_POSSIBLE
	select KVM_GENERIC_MMU_NOTIFIER
	select CMA
	help
	  Support running unmodified book3s_64 guest kernels in
	  virtual machines on POWER7 and newer processors that have
	  hypervisor mode available to the host.

	  If you say Y here, KVM will use the hardware virtualization
	  facilities of POWER7 (and later) processors, meaning that
	  guest operating systems will run at full hardware speed
	  using supervisor and user modes.  However, this also means
	  that KVM is not usable under PowerVM (pHyp), is only usable
	  on POWER7 or later processors, and cannot emulate a
	  different processor from the host processor.

	  If unsure, say N.

config KVM_BOOK3S_64_PR
	tristate "KVM support without using hypervisor mode in host"
	depends on KVM_BOOK3S_64
	depends on !CONTEXT_TRACKING_USER
	select KVM_BOOK3S_PR_POSSIBLE
	help
	  Support running guest kernels in virtual machines on processors
	  without using hypervisor mode in the host, by running the
	  guest in user mode (problem state) and emulating all
	  privileged instructions and registers.

	  This is only available for hash MMU mode and only supports
	  guests that use hash MMU mode.

	  This is not as fast as using hypervisor mode, but works on
	  machines where hypervisor mode is not available or not usable,
	  and can emulate processors that are different from the host
	  processor, including emulating 32-bit processors on a 64-bit
	  host.

	  Selecting this option will cause the SCV facility to be
	  disabled when the kernel is booted on the pseries platform in
	  hash MMU mode (regardless of PR VMs running). When any PR VMs
	  are running, "AIL" mode is disabled which may slow interrupts
	  and system calls on the host.

config KVM_BOOK3S_HV_EXIT_TIMING
	bool

config KVM_BOOK3S_HV_P9_TIMING
	bool "Detailed timing for the P9 entry point"
	select KVM_BOOK3S_HV_EXIT_TIMING
	depends on KVM_BOOK3S_HV_POSSIBLE && DEBUG_FS
	help
	  Calculate time taken for each vcpu during vcpu entry and
	  exit, time spent inside the guest and time spent handling
	  hypercalls and page faults. The total, minimum and maximum
	  times in nanoseconds together with the number of executions
	  are reported in debugfs in kvm/vm#/vcpu#/timings.

	  If unsure, say N.

config KVM_BOOK3S_HV_P8_TIMING
	bool "Detailed timing for hypervisor real-mode code (for POWER8)"
	select KVM_BOOK3S_HV_EXIT_TIMING
	depends on KVM_BOOK3S_HV_POSSIBLE && DEBUG_FS && !KVM_BOOK3S_HV_P9_TIMING
	help
	  Calculate time taken for each vcpu in the real-mode guest entry,
	  exit, and interrupt handling code, plus time spent in the guest
	  and in nap mode due to idle (cede) while other threads are still
	  in the guest.  The total, minimum and maximum times in nanoseconds
	  together with the number of executions are reported in debugfs in
	  kvm/vm#/vcpu#/timings.  The overhead is of the order of 30 - 40
	  ns per exit on POWER8.

	  If unsure, say N.

config KVM_BOOK3S_HV_NESTED_PMU_WORKAROUND
	bool "Nested L0 host workaround for L1 KVM host PMU handling bug" if EXPERT
	depends on KVM_BOOK3S_HV_POSSIBLE
	default !EXPERT
	help
	  Old nested HV capable Linux guests have a bug where they don't
	  reflect the PMU in-use status of their L2 guest to the L0 host
	  while the L2 PMU registers are live. This can result in loss
	  of L2 PMU register state, causing perf to not work correctly in
	  L2 guests.

	  Selecting this option for the L0 host implements a workaround for
	  those buggy L1s which saves the L2 state, at the cost of performance
	  in all nested-capable guest entry/exit.

config KVM_BOOKE_HV
	bool

config KVM_EXIT_TIMING
	bool "Detailed exit timing"
	depends on KVM_E500V2 || KVM_E500MC
	help
	  Calculate elapsed time for every exit/enter cycle. A per-vcpu
	  report is available in debugfs kvm/vm#_vcpu#_timing.
	  The overhead is relatively small, however it is not recommended for
	  production environments.

	  If unsure, say N.

config KVM_E500V2
	bool "KVM support for PowerPC E500v2 processors"
	depends on PPC_E500 && !PPC_E500MC
	depends on !CONTEXT_TRACKING_USER
	select KVM
	select KVM_MMIO
	select KVM_GENERIC_MMU_NOTIFIER
	help
	  Support running unmodified E500 guest kernels in virtual machines on
	  E500v2 host processors.

	  This module provides access to the hardware capabilities through
	  a character device node named /dev/kvm.

	  If unsure, say N.

config KVM_E500MC
	bool "KVM support for PowerPC E500MC/E5500/E6500 processors"
	depends on PPC_E500MC
	depends on !CONTEXT_TRACKING_USER
	select KVM
	select KVM_MMIO
	select KVM_BOOKE_HV
	select KVM_GENERIC_MMU_NOTIFIER
	help
	  Support running unmodified E500MC/E5500/E6500 guest kernels in
	  virtual machines on E500MC/E5500/E6500 host processors.

	  This module provides access to the hardware capabilities through
	  a character device node named /dev/kvm.

	  If unsure, say N.

config KVM_MPIC
	bool "KVM in-kernel MPIC emulation"
	depends on KVM && PPC_E500
	select HAVE_KVM_IRQCHIP
	select HAVE_KVM_IRQ_ROUTING
	select HAVE_KVM_MSI
	help
	  Enable support for emulating MPIC devices inside the
	  host kernel, rather than relying on userspace to emulate.
	  Currently, support is limited to certain versions of
	  Freescale's MPIC implementation.

config KVM_XICS
	bool "KVM in-kernel XICS emulation"
	depends on KVM_BOOK3S_64 && !KVM_MPIC
	select HAVE_KVM_IRQCHIP
	default y
	help
	  Include support for the XICS (eXternal Interrupt Controller
	  Specification) interrupt controller architecture used on
	  IBM POWER (pSeries) servers.

config KVM_XIVE
	bool
	default y
	depends on KVM_XICS && PPC_XIVE_NATIVE && KVM_BOOK3S_HV_POSSIBLE

endif # VIRTUALIZATION
