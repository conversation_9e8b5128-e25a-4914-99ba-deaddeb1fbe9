# SPDX-License-Identifier: GPL-2.0

config PPC_DISABLE_WERROR
	bool "Don't build arch/powerpc code with -Werror"
	help
	  This option tells the compiler NOT to build the code under
	  arch/powerpc with the -Werror flag (which means warnings
	  are treated as errors).

	  Only enable this if you are hitting a build failure in the
	  arch/powerpc code caused by a warning, and you don't feel
	  inclined to fix it.

config PPC_WERROR
	bool
	depends on !PPC_DISABLE_WERROR
	default y

config PRINT_STACK_DEPTH
	int "Stack depth to print" if DEBUG_KERNEL
	default 64
	help
	  This option allows you to set the stack depth that the kernel
	  prints in stack traces. This can be useful if your display is
	  too small and stack traces cause important information to
	  scroll off the screen.

config HCALL_STATS
	bool "Hypervisor call instrumentation"
	depends on PPC_PSERIES && DEBUG_FS && TRACEPOINTS
	help
	  Adds code to keep track of the number of hypervisor calls made and
	  the amount of time spent in hypervisor calls.  Wall time spent in
	  each call is always calculated, and if available CPU cycles spent
	  are also calculated.  A directory named hcall_inst is added at the
	  root of the debugfs filesystem.  Within the hcall_inst directory
	  are files that contain CPU specific call statistics.

	  This option will add a small amount of overhead to all hypervisor
	  calls.

config PPC_EMULATED_STATS
	bool "Emulated instructions tracking"
	depends on DEBUG_FS
	help
	  Adds code to keep track of the number of instructions that are
	  emulated by the in-kernel emulator. Counters for the various classes
	  of emulated instructions are available under
	  powerpc/emulated_instructions/ in the root of the debugfs file
	  system. Optionally (controlled by
	  powerpc/emulated_instructions/do_warn in debugfs), rate-limited
	  warnings can be printed to the console when instructions are
	  emulated.

config CODE_PATCHING_SELFTEST
	bool "Run self-tests of the code-patching code"
	depends on DEBUG_KERNEL

config JUMP_LABEL_FEATURE_CHECKS
	bool "Enable use of jump label for cpu/mmu_has_feature()"
	depends on JUMP_LABEL
	default y
	help
	  Selecting this options enables use of jump labels for some internal
	  feature checks. This should generate more optimal code for those
	  checks.

config JUMP_LABEL_FEATURE_CHECK_DEBUG
	bool "Do extra check on feature fixup calls"
	depends on DEBUG_KERNEL && JUMP_LABEL_FEATURE_CHECKS
	help
	  This tries to catch incorrect usage of cpu_has_feature() and
	  mmu_has_feature() in the code.

	  If you don't know what this means, say N.

config FTR_FIXUP_SELFTEST
	bool "Run self-tests of the feature-fixup code"
	depends on DEBUG_KERNEL

config MSI_BITMAP_SELFTEST
	bool "Run self-tests of the MSI bitmap code"
	depends on DEBUG_KERNEL

config GUEST_STATE_BUFFER_TEST
	def_tristate n
	prompt "Enable Guest State Buffer unit tests"
	depends on KUNIT
	depends on KVM_BOOK3S_HV_POSSIBLE
	default KUNIT_ALL_TESTS
	help
	  The Guest State Buffer is a data format specified in the PAPR.
	  It is by hcalls to communicate the state of L2 guests between
	  the L1 and L0 hypervisors. Enable unit tests for the library
	  used to create and use guest state buffers.

config PPC_IRQ_SOFT_MASK_DEBUG
	bool "Include extra checks for powerpc irq soft masking"
	depends on PPC64

config PPC_RFI_SRR_DEBUG
	bool "Include extra checks for RFI SRR register validity"
	depends on PPC_BOOK3S_64

config XMON
	bool "Include xmon kernel debugger"
	depends on DEBUG_KERNEL
	select CONSOLE_POLL if SERIAL_CPM_CONSOLE
	help
	  Include in-kernel hooks for the xmon kernel monitor/debugger.
	  Unless you are intending to debug the kernel, say N here.
	  Make sure to enable also CONFIG_BOOTX_TEXT on Macs. Otherwise
	  nothing will appear on the screen (xmon writes directly to the
	  framebuffer memory).
	  The cmdline option 'xmon' or 'xmon=early' will drop into xmon
	  very early during boot. 'xmon=on' will just enable the xmon
	  debugger hooks.  'xmon=off' will disable the debugger hooks
	  if CONFIG_XMON_DEFAULT is set.
	  xmon will print a backtrace on the very first invocation.
	  'xmon=nobt' will disable this autobacktrace.

config XMON_DEFAULT
	bool "Enable xmon by default"
	depends on XMON
	help
	  xmon is normally disabled unless booted with 'xmon=on'.
	  Use 'xmon=off' to disable xmon init during runtime.

config XMON_DISASSEMBLY
	bool "Include disassembly support in xmon"
	depends on XMON
	default y
	help
	  Include support for disassembling in xmon. You probably want
	  to say Y here, unless you're building for a memory-constrained
	  system.

config XMON_DEFAULT_RO_MODE
	bool "Restrict xmon to read-only operations by default"
	depends on XMON
	default y
	help
	  Operate xmon in read-only mode. The cmdline options 'xmon=rw' and
	  'xmon=ro' override this default.

config DEBUGGER
	bool
	depends on KGDB || XMON
	default y

config BDI_SWITCH
	bool "Include BDI-2000 user context switcher"
	depends on DEBUG_KERNEL && PPC32
	help
	  Include in-kernel support for the Abatron BDI2000 debugger.
	  Unless you are intending to debug the kernel with one of these
	  machines, say N here.

config BOOTX_TEXT
	bool "Support for early boot text console (BootX or OpenFirmware only)"
	depends on PPC_BOOK3S
	select FONT_SUN8x16
	select FONT_SUPPORT
	help
	  Say Y here to see progress messages from the boot firmware in text
	  mode. Requires either BootX or Open Firmware.

config PPC_EARLY_DEBUG
	bool "Early debugging (dangerous)"
	help
	  Say Y to enable some early debugging facilities that may be available
	  for your processor/board combination. Those facilities are hacks
	  intended to debug problems early during boot, this should not be
	  enabled in a production kernel.
	  Note that enabling this will also cause the kernel default log level
	  to be pushed to max automatically very early during boot

choice
	prompt "Early debugging console"
	depends on PPC_EARLY_DEBUG
	help
	  Use the selected console for early debugging. Careful, if you
	  enable debugging for the wrong type of machine your kernel
	  _will not boot_.

config PPC_EARLY_DEBUG_BOOTX
	bool "BootX or OpenFirmware"
	depends on BOOTX_TEXT
	help
	  Select this to enable early debugging for a machine using BootX
	  or OpenFirmware.

config PPC_EARLY_DEBUG_LPAR
	bool "LPAR HV Console"
	depends on PPC_PSERIES && HVC_CONSOLE
	help
	  Select this to enable early debugging for a machine with a HVC
	  console on vterm 0.

config PPC_EARLY_DEBUG_LPAR_HVSI
	bool "LPAR HVSI Console"
	depends on PPC_PSERIES && HVC_CONSOLE
	help
	  Select this to enable early debugging for a machine with a HVSI
	  console on a specified vterm.

config PPC_EARLY_DEBUG_G5
	bool "Apple G5"
	depends on PPC_PMAC64
	help
	  Select this to enable early debugging for Apple G5 machines.

config PPC_EARLY_DEBUG_RTAS_PANEL
	bool "RTAS Panel"
	depends on PPC_RTAS
	help
	  Select this to enable early debugging via the RTAS panel.

config PPC_EARLY_DEBUG_RTAS_CONSOLE
	bool "RTAS Console"
	depends on PPC_RTAS
	select UDBG_RTAS_CONSOLE
	help
	  Select this to enable early debugging via the RTAS console.

config PPC_EARLY_DEBUG_MAPLE
	bool "Maple real mode"
	depends on PPC_MAPLE
	help
	  Select this to enable early debugging for Maple.

config PPC_EARLY_DEBUG_PAS_REALMODE
	bool "PA Semi real mode"
	depends on PPC_PASEMI
	help
	  Select this to enable early debugging for PA Semi.
	  Output will be on UART0.

config PPC_EARLY_DEBUG_44x
	bool "Early serial debugging for IBM/AMCC 44x CPUs"
	depends on 44x
	help
	  Select this to enable early debugging for IBM 44x chips via the
	  inbuilt serial port.  If you enable this, ensure you set
	  PPC_EARLY_DEBUG_44x_PHYSLOW below to suit your target board.

config PPC_EARLY_DEBUG_CPM
	bool "Early serial debugging for Freescale CPM-based serial ports"
	depends on SERIAL_CPM=y
	help
	  Select this to enable early debugging for Freescale chips
	  using a CPM-based serial port.  This assumes that the bootwrapper
	  has run, and set up the CPM in a particular way.

config PPC_EARLY_DEBUG_USBGECKO
	bool "Early debugging through the USB Gecko adapter"
	depends on GAMECUBE_COMMON
	select USBGECKO_UDBG
	help
	  Select this to enable early debugging for Nintendo GameCube/Wii
	  consoles via an external USB Gecko adapter.

config PPC_EARLY_DEBUG_PS3GELIC
	bool "Early debugging through the PS3 Ethernet port"
	depends on PPC_PS3
	help
	  Select this to enable early debugging for the PlayStation3 via
	  UDP broadcasts sent out through the Ethernet port.

config PPC_EARLY_DEBUG_OPAL_RAW
	bool "OPAL raw console"
	depends on HVC_OPAL
	help
	  Select this to enable early debugging for the PowerNV platform
	  using a "raw" console

config PPC_EARLY_DEBUG_OPAL_HVSI
	bool "OPAL hvsi console"
	depends on HVC_OPAL
	help
	  Select this to enable early debugging for the PowerNV platform
	  using an "hvsi" console

config PPC_EARLY_DEBUG_MEMCONS
	bool "In memory console"
	help
	  Select this to enable early debugging using an in memory console.
	  This console provides input and output buffers stored within the
	  kernel BSS and should be safe to select on any system. A debugger
	  can then be used to read kernel output or send input to the console.

config PPC_EARLY_DEBUG_16550
	bool "Serial 16550"
	depends on PPC_UDBG_16550
	help
	  Select this to enable early debugging via Serial 16550 console
endchoice

config PPC_MEMCONS_OUTPUT_SIZE
	int "In memory console output buffer size"
	depends on PPC_EARLY_DEBUG_MEMCONS
	default 4096
	help
	  Selects the size of the output buffer (in bytes) of the in memory
	  console.

config PPC_MEMCONS_INPUT_SIZE
	int "In memory console input buffer size"
	depends on PPC_EARLY_DEBUG_MEMCONS
	default 128
	help
	  Selects the size of the input buffer (in bytes) of the in memory
	  console.

config PPC_EARLY_DEBUG_OPAL
	def_bool y
	depends on PPC_EARLY_DEBUG_OPAL_RAW || PPC_EARLY_DEBUG_OPAL_HVSI

config PPC_EARLY_DEBUG_HVSI_VTERMNO
	hex "vterm number to use with early debug HVSI"
	depends on PPC_EARLY_DEBUG_LPAR_HVSI
	default "0x30000000"
	help
	  You probably want 0x30000000 for your first serial port and
	  0x30000001 for your second one

config PPC_EARLY_DEBUG_OPAL_VTERMNO
	hex "vterm number to use with OPAL early debug"
	depends on PPC_EARLY_DEBUG_OPAL
	default "0"
	help
	  This correspond to which /dev/hvcN you want to use for early
	  debug.

	  On OPAL v2, this will be 0 for network console and 1 or 2 for
	  the machine built-in serial ports.

config PPC_EARLY_DEBUG_44x_PHYSLOW
	hex "Low 32 bits of early debug UART physical address"
	depends on PPC_EARLY_DEBUG_44x
	default "0x40000200"
	help
	  You probably want 0x40000200 for ebony boards and
	  0x40000300 for taishan

config PPC_EARLY_DEBUG_44x_PHYSHIGH
	hex "EPRN of early debug UART physical address"
	depends on PPC_EARLY_DEBUG_44x
	default "0x1"

config PPC_EARLY_DEBUG_CPM_ADDR
	hex "CPM UART early debug transmit descriptor address"
	depends on PPC_EARLY_DEBUG_CPM
	default "0xfa202008" if PPC_EP88XC
	default "0xf0001ff8" if CPM2
	default "0xff002008" if CPM1
	help
	  This specifies the address of the transmit descriptor
	  used for early debug output.  Because it is needed before
	  platform probing is done, all platforms selected must
	  share the same address.

config PPC_EARLY_DEBUG_16550_PHYSADDR
	hex "Early debug Serial 16550 physical address"
	depends on PPC_EARLY_DEBUG_16550

config PPC_EARLY_DEBUG_16550_STRIDE
	int "Early debug Serial 16550 stride"
	depends on PPC_EARLY_DEBUG_16550
	default 1

config FAIL_IOMMU
	bool "Fault-injection capability for IOMMU"
	depends on FAULT_INJECTION
	depends on PCI || IBMVIO
	help
	  Provide fault-injection capability for IOMMU. Each device can
	  be selectively enabled via the fail_iommu property.

	  If you are unsure, say N.

config PPC_FAST_ENDIAN_SWITCH
	bool "Deprecated fast endian-switch syscall"
	depends on DEBUG_KERNEL && PPC_BOOK3S_64
	help
	  If you're unsure what this is, say N.

config KASAN_SHADOW_OFFSET
	hex
	depends on KASAN
	default 0xe0000000 if PPC32
	default 0xa80e000000000000 if PPC_BOOK3S_64
	default 0xa8001c0000000000 if PPC_BOOK3E_64
