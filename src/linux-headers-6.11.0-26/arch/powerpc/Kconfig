# SPDX-License-Identifier: GPL-2.0
source "arch/powerpc/platforms/Kconfig.cputype"

config CC_HAS_ELFV2
	def_bool PPC64 && $(cc-option, -mabi=elfv2)

config CC_HAS_PREFIXED
	def_bool PPC64 && $(cc-option, -mcpu=power10 -mprefixed)

config CC_HAS_PCREL
	# Clang has a bug (https://github.com/llvm/llvm-project/issues/62372)
	# where pcrel code is not generated if -msoft-float, -mno-altivec, or
	# -mno-vsx options are also given. Without these options, fp/vec
	# instructions are generated from regular kernel code. So Clang can't
	# do pcrel yet.
	def_bool PPC64 && CC_IS_GCC && $(cc-option, -mcpu=power10 -mpcrel)

config 32BIT
	bool
	default y if PPC32

config 64BIT
	bool
	default y if PPC64

config LIVEPATCH_64
	def_bool PPC64
	depends on LIVEPATCH

config MMU
	bool
	default y

config ARCH_MMAP_RND_BITS_MAX
	# On Book3S 64, the default virtual address space for 64-bit processes
	# is 2^47 (128TB). As a maximum, allow randomisation to consume up to
	# 32T of address space (2^45), which should ensure a reasonable gap
	# between bottom-up and top-down allocations for applications that
	# consume "normal" amounts of address space. Book3S 64 only supports 64K
	# and 4K page sizes.
	default 29 if PPC_BOOK3S_64 && PPC_64K_PAGES # 29 = 45 (32T) - 16 (64K)
	default 33 if PPC_BOOK3S_64		     # 33 = 45 (32T) - 12 (4K)
	#
	# On all other 64-bit platforms (currently only Book3E), the virtual
	# address space is 2^46 (64TB). Allow randomisation to consume up to 16T
	# of address space (2^44). Only 4K page sizes are supported.
	default 32 if 64BIT	# 32 = 44 (16T) - 12 (4K)
	#
	# For 32-bit, use the compat values, as they're the same.
	default ARCH_MMAP_RND_COMPAT_BITS_MAX

config ARCH_MMAP_RND_BITS_MIN
	# Allow randomisation to consume up to 1GB of address space (2^30).
	default 14 if 64BIT && PPC_64K_PAGES	# 14 = 30 (1GB) - 16 (64K)
	default 18 if 64BIT			# 18 = 30 (1GB) - 12 (4K)
	#
	# For 32-bit, use the compat values, as they're the same.
	default ARCH_MMAP_RND_COMPAT_BITS_MIN

config ARCH_MMAP_RND_COMPAT_BITS_MAX
	# Total virtual address space for 32-bit processes is 2^31 (2GB).
	# Allow randomisation to consume up to 512MB of address space (2^29).
	default 11 if PPC_256K_PAGES	# 11 = 29 (512MB) - 18 (256K)
	default 13 if PPC_64K_PAGES	# 13 = 29 (512MB) - 16 (64K)
	default 15 if PPC_16K_PAGES	# 15 = 29 (512MB) - 14 (16K)
	default 17			# 17 = 29 (512MB) - 12 (4K)

config ARCH_MMAP_RND_COMPAT_BITS_MIN
	# Total virtual address space for 32-bit processes is 2^31 (2GB).
	# Allow randomisation to consume up to 8MB of address space (2^23).
	default 5 if PPC_256K_PAGES	#  5 = 23 (8MB) - 18 (256K)
	default 7 if PPC_64K_PAGES	#  7 = 23 (8MB) - 16 (64K)
	default 9 if PPC_16K_PAGES	#  9 = 23 (8MB) - 14 (16K)
	default 11			# 11 = 23 (8MB) - 12 (4K)

config NR_IRQS
	int "Number of virtual interrupt numbers"
	range 32 1048576
	default "512"
	help
	  This defines the number of virtual interrupt numbers the kernel
	  can manage. Virtual interrupt numbers are what you see in
	  /proc/interrupts. If you configure your system to have too few,
	  drivers will fail to load or worse - handle with care.

config NMI_IPI
	bool
	depends on SMP && (DEBUGGER || KEXEC_CORE || HARDLOCKUP_DETECTOR)
	default y

config PPC_WATCHDOG
	bool
	depends on HARDLOCKUP_DETECTOR_ARCH
	default y
	help
	  This is a placeholder when the powerpc hardlockup detector
	  watchdog is selected (arch/powerpc/kernel/watchdog.c). It is
	  selected via the generic lockup detector menu which is why we
	  have no standalone config option for it here.

config STACKTRACE_SUPPORT
	bool
	default y

config LOCKDEP_SUPPORT
	bool
	default y

config GENERIC_LOCKBREAK
	bool
	default y
	depends on SMP && PREEMPTION && !PPC_QUEUED_SPINLOCKS

config GENERIC_HWEIGHT
	bool
	default y

config PPC
	bool
	default y
	#
	# Please keep this list sorted alphabetically.
	#
	select ARCH_32BIT_OFF_T if PPC32
	select ARCH_DISABLE_KASAN_INLINE	if PPC_RADIX_MMU
	select ARCH_DMA_DEFAULT_COHERENT	if !NOT_COHERENT_CACHE
	select ARCH_ENABLE_MEMORY_HOTPLUG
	select ARCH_ENABLE_MEMORY_HOTREMOVE
	select ARCH_HAS_COPY_MC			if PPC64
	select ARCH_HAS_CURRENT_STACK_POINTER
	select ARCH_HAS_DEBUG_VIRTUAL
	select ARCH_HAS_DEBUG_VM_PGTABLE
	select ARCH_HAS_DEBUG_WX		if STRICT_KERNEL_RWX
	select ARCH_HAS_DEVMEM_IS_ALLOWED
	select ARCH_HAS_DMA_MAP_DIRECT 		if PPC_PSERIES
	select ARCH_HAS_FORTIFY_SOURCE
	select ARCH_HAS_GCOV_PROFILE_ALL
	select ARCH_HAS_KCOV
	select ARCH_HAS_KERNEL_FPU_SUPPORT	if PPC64 && PPC_FPU
	select ARCH_HAS_MEMBARRIER_CALLBACKS
	select ARCH_HAS_MEMBARRIER_SYNC_CORE
	select ARCH_HAS_MEMREMAP_COMPAT_ALIGN	if PPC_64S_HASH_MMU
	select ARCH_HAS_MMIOWB			if PPC64
	select ARCH_HAS_NON_OVERLAPPING_ADDRESS_SPACE
	select ARCH_HAS_PHYS_TO_DMA
	select ARCH_HAS_PMEM_API
	select ARCH_HAS_PTE_DEVMAP		if PPC_BOOK3S_64
	select ARCH_HAS_PTE_SPECIAL
	select ARCH_HAS_SCALED_CPUTIME		if VIRT_CPU_ACCOUNTING_NATIVE && PPC_BOOK3S_64
	select ARCH_HAS_SET_MEMORY
	select ARCH_HAS_STRICT_KERNEL_RWX	if (PPC_BOOK3S || PPC_8xx) && !HIBERNATION
	select ARCH_HAS_STRICT_KERNEL_RWX	if PPC_85xx && !HIBERNATION && !RANDOMIZE_BASE
	select ARCH_HAS_STRICT_MODULE_RWX	if ARCH_HAS_STRICT_KERNEL_RWX
	select ARCH_HAS_SYSCALL_WRAPPER		if !SPU_BASE && !COMPAT
	select ARCH_HAS_TICK_BROADCAST		if GENERIC_CLOCKEVENTS_BROADCAST
	select ARCH_HAS_UACCESS_FLUSHCACHE
	select ARCH_HAS_UBSAN
	select ARCH_HAVE_NMI_SAFE_CMPXCHG
	select ARCH_HAVE_EXTRA_ELF_NOTES        if SPU_BASE
	select ARCH_KEEP_MEMBLOCK
	select ARCH_MHP_MEMMAP_ON_MEMORY_ENABLE	if PPC_RADIX_MMU
	select ARCH_MIGHT_HAVE_PC_PARPORT
	select ARCH_MIGHT_HAVE_PC_SERIO
	select ARCH_OPTIONAL_KERNEL_RWX		if ARCH_HAS_STRICT_KERNEL_RWX
	select ARCH_OPTIONAL_KERNEL_RWX_DEFAULT
	select ARCH_SPLIT_ARG64			if PPC32
	select ARCH_STACKWALK
	select ARCH_SUPPORTS_ATOMIC_RMW
	select ARCH_SUPPORTS_DEBUG_PAGEALLOC	if PPC_BOOK3S || PPC_8xx
	select ARCH_USE_BUILTIN_BSWAP
	select ARCH_USE_CMPXCHG_LOCKREF		if PPC64
	select ARCH_USE_MEMTEST
	select ARCH_USE_QUEUED_RWLOCKS		if PPC_QUEUED_SPINLOCKS
	select ARCH_WANT_DEFAULT_BPF_JIT
	select ARCH_WANT_DEFAULT_TOPDOWN_MMAP_LAYOUT
	select ARCH_WANT_IPC_PARSE_VERSION
	select ARCH_WANT_IRQS_OFF_ACTIVATE_MM
	select ARCH_WANT_LD_ORPHAN_WARN
	select ARCH_WANT_OPTIMIZE_DAX_VMEMMAP	if PPC_RADIX_MMU
	select ARCH_WANTS_MODULES_DATA_IN_VMALLOC	if PPC_BOOK3S_32 || PPC_8xx
	select ARCH_WEAK_RELEASE_ACQUIRE
	select BINFMT_ELF
	select BUILDTIME_TABLE_SORT
	select CLONE_BACKWARDS
	select CPUMASK_OFFSTACK			if NR_CPUS >= 8192
	select DCACHE_WORD_ACCESS		if PPC64 && CPU_LITTLE_ENDIAN
	select DMA_OPS_BYPASS			if PPC64
	select DMA_OPS				if PPC64
	select DYNAMIC_FTRACE			if FUNCTION_TRACER
	select EDAC_ATOMIC_SCRUB
	select EDAC_SUPPORT
	select FTRACE_MCOUNT_USE_PATCHABLE_FUNCTION_ENTRY if ARCH_USING_PATCHABLE_FUNCTION_ENTRY
	select FUNCTION_ALIGNMENT_4B
	select GENERIC_ATOMIC64			if PPC32
	select GENERIC_CLOCKEVENTS_BROADCAST	if SMP
	select GENERIC_CMOS_UPDATE
	select GENERIC_CPU_AUTOPROBE
	select GENERIC_CPU_VULNERABILITIES	if PPC_BARRIER_NOSPEC
	select GENERIC_EARLY_IOREMAP
	select GENERIC_GETTIMEOFDAY
	select GENERIC_IDLE_POLL_SETUP
	select GENERIC_IOREMAP
	select GENERIC_IRQ_SHOW
	select GENERIC_IRQ_SHOW_LEVEL
	select GENERIC_PCI_IOMAP		if PCI
	select GENERIC_PTDUMP
	select GENERIC_SMP_IDLE_THREAD
	select GENERIC_TIME_VSYSCALL
	select GENERIC_VDSO_TIME_NS
	select HAS_IOPORT			if PCI
	select HAVE_ARCH_AUDITSYSCALL
	select HAVE_ARCH_HUGE_VMALLOC		if HAVE_ARCH_HUGE_VMAP
	select HAVE_ARCH_HUGE_VMAP		if PPC_RADIX_MMU || PPC_8xx
	select HAVE_ARCH_JUMP_LABEL
	select HAVE_ARCH_JUMP_LABEL_RELATIVE
	select HAVE_ARCH_KASAN			if PPC32 && PAGE_SHIFT <= 14
	select HAVE_ARCH_KASAN			if PPC_RADIX_MMU
	select HAVE_ARCH_KASAN			if PPC_BOOK3E_64
	select HAVE_ARCH_KASAN_VMALLOC		if HAVE_ARCH_KASAN
	select HAVE_ARCH_KCSAN
	select HAVE_ARCH_KFENCE			if ARCH_SUPPORTS_DEBUG_PAGEALLOC
	select HAVE_ARCH_RANDOMIZE_KSTACK_OFFSET
	select HAVE_ARCH_WITHIN_STACK_FRAMES
	select HAVE_ARCH_KGDB
	select HAVE_ARCH_MMAP_RND_BITS
	select HAVE_ARCH_MMAP_RND_COMPAT_BITS	if COMPAT
	select HAVE_ARCH_NVRAM_OPS
	select HAVE_ARCH_SECCOMP_FILTER
	select HAVE_ARCH_TRACEHOOK
	select HAVE_ASM_MODVERSIONS
	select HAVE_CONTEXT_TRACKING_USER
	select HAVE_C_RECORDMCOUNT
	select HAVE_DEBUG_KMEMLEAK
	select HAVE_DEBUG_STACKOVERFLOW
	select HAVE_DYNAMIC_FTRACE
	select HAVE_DYNAMIC_FTRACE_WITH_ARGS	if ARCH_USING_PATCHABLE_FUNCTION_ENTRY || MPROFILE_KERNEL || PPC32
	select HAVE_DYNAMIC_FTRACE_WITH_REGS	if ARCH_USING_PATCHABLE_FUNCTION_ENTRY || MPROFILE_KERNEL || PPC32
	select HAVE_EBPF_JIT
	select HAVE_EFFICIENT_UNALIGNED_ACCESS
	select HAVE_GUP_FAST
	select HAVE_FTRACE_MCOUNT_RECORD
	select HAVE_FUNCTION_ARG_ACCESS_API
	select HAVE_FUNCTION_DESCRIPTORS	if PPC64_ELF_ABI_V1
	select HAVE_FUNCTION_ERROR_INJECTION
	select HAVE_FUNCTION_GRAPH_TRACER
	select HAVE_FUNCTION_TRACER		if PPC64 || (PPC32 && CC_IS_GCC)
	select HAVE_GCC_PLUGINS			if GCC_VERSION >= 50200   # plugin support on gcc <= 5.1 is buggy on PPC
	select HAVE_GENERIC_VDSO
	select HAVE_HARDLOCKUP_DETECTOR_ARCH	if PPC_BOOK3S_64 && SMP
	select HAVE_HARDLOCKUP_DETECTOR_PERF	if PERF_EVENTS && HAVE_PERF_EVENTS_NMI
	select HAVE_HW_BREAKPOINT		if PERF_EVENTS && (PPC_BOOK3S || PPC_8xx)
	select HAVE_IOREMAP_PROT
	select HAVE_IRQ_TIME_ACCOUNTING
	select HAVE_KERNEL_GZIP
	select HAVE_KERNEL_LZMA			if DEFAULT_UIMAGE
	select HAVE_KERNEL_LZO			if DEFAULT_UIMAGE
	select HAVE_KERNEL_XZ			if PPC_BOOK3S || 44x
	select HAVE_KPROBES
	select HAVE_KPROBES_ON_FTRACE
	select HAVE_KRETPROBES
	select HAVE_LD_DEAD_CODE_DATA_ELIMINATION if HAVE_OBJTOOL_MCOUNT && (!ARCH_USING_PATCHABLE_FUNCTION_ENTRY || (!CC_IS_GCC || GCC_VERSION >= 110100))
	select HAVE_LIVEPATCH			if HAVE_DYNAMIC_FTRACE_WITH_REGS
	select HAVE_MOD_ARCH_SPECIFIC
	select HAVE_NMI				if PERF_EVENTS || (PPC64 && PPC_BOOK3S)
	select HAVE_OPTPROBES
	select HAVE_OBJTOOL			if ARCH_USING_PATCHABLE_FUNCTION_ENTRY || MPROFILE_KERNEL || PPC32
	select HAVE_OBJTOOL_MCOUNT		if HAVE_OBJTOOL
	select HAVE_PERF_EVENTS
	select HAVE_PERF_EVENTS_NMI		if PPC64
	select HAVE_PERF_REGS
	select HAVE_PERF_USER_STACK_DUMP
	select HAVE_REGS_AND_STACK_ACCESS_API
	select HAVE_RELIABLE_STACKTRACE
	select HAVE_RSEQ
	select HAVE_SETUP_PER_CPU_AREA		if PPC64
	select HAVE_SOFTIRQ_ON_OWN_STACK
	select HAVE_STACKPROTECTOR		if PPC32 && $(cc-option,$(m32-flag) -mstack-protector-guard=tls -mstack-protector-guard-reg=r2 -mstack-protector-guard-offset=0)
	select HAVE_STACKPROTECTOR		if PPC64 && $(cc-option,$(m64-flag) -mstack-protector-guard=tls -mstack-protector-guard-reg=r13 -mstack-protector-guard-offset=0)
	select HAVE_STATIC_CALL			if PPC32
	select HAVE_SYSCALL_TRACEPOINTS
	select HAVE_VIRT_CPU_ACCOUNTING
	select HAVE_VIRT_CPU_ACCOUNTING_GEN
	select HOTPLUG_SMT			if HOTPLUG_CPU
	select SMT_NUM_THREADS_DYNAMIC
	select HUGETLB_PAGE_SIZE_VARIABLE	if PPC_BOOK3S_64 && HUGETLB_PAGE
	select IOMMU_HELPER			if PPC64
	select IRQ_DOMAIN
	select IRQ_FORCED_THREADING
	select KASAN_VMALLOC			if KASAN && EXECMEM
	select LOCK_MM_AND_FIND_VMA
	select MMU_GATHER_PAGE_SIZE
	select MMU_GATHER_RCU_TABLE_FREE
	select MMU_GATHER_MERGE_VMAS
	select MMU_LAZY_TLB_SHOOTDOWN		if PPC_BOOK3S_64
	select MODULES_USE_ELF_RELA
	select NEED_DMA_MAP_STATE		if PPC64 || NOT_COHERENT_CACHE
	select NEED_PER_CPU_EMBED_FIRST_CHUNK	if PPC64
	select NEED_PER_CPU_PAGE_FIRST_CHUNK	if PPC64
	select NEED_SG_DMA_LENGTH
	select OF
	select OF_EARLY_FLATTREE
	select OLD_SIGACTION			if PPC32
	select OLD_SIGSUSPEND
	select PCI_DOMAINS			if PCI
	select PCI_MSI_ARCH_FALLBACKS		if PCI_MSI
	select PCI_SYSCALL			if PCI
	select PPC_DAWR				if PPC64
	select RTC_LIB
	select SPARSE_IRQ
	select STRICT_KERNEL_RWX if STRICT_MODULE_RWX
	select SYSCTL_EXCEPTION_TRACE
	select THREAD_INFO_IN_TASK
	select TRACE_IRQFLAGS_SUPPORT
	#
	# Please keep this list sorted alphabetically.
	#

config PPC_BARRIER_NOSPEC
	bool
	default y
	depends on PPC_BOOK3S_64 || PPC_E500

config PPC_HAS_LBARX_LHARX
	bool

config EARLY_PRINTK
	bool
	default y

config PANIC_TIMEOUT
	int
	default 180

config COMPAT
	bool "Enable support for 32bit binaries"
	depends on PPC64
	default y if !CPU_LITTLE_ENDIAN
	select ARCH_WANT_OLD_COMPAT_IPC
	select COMPAT_OLD_SIGACTION

config SCHED_OMIT_FRAME_POINTER
	bool
	default y

config ARCH_MAY_HAVE_PC_FDC
	bool
	default PCI

config PPC_UDBG_16550
	bool

config GENERIC_TBSYNC
	bool
	default y if PPC32 && SMP

config AUDIT_ARCH
	bool
	default y

config GENERIC_BUG
	bool
	default y
	depends on BUG

config GENERIC_BUG_RELATIVE_POINTERS
	def_bool y
	depends on GENERIC_BUG

config SYS_SUPPORTS_APM_EMULATION
	default y if PMAC_APM_EMU
	bool

config EPAPR_BOOT
	bool
	help
	  Used to allow a board to specify it wants an ePAPR compliant wrapper.

config DEFAULT_UIMAGE
	bool
	help
	  Used to allow a board to specify it wants a uImage built by default

config ARCH_HIBERNATION_POSSIBLE
	bool
	default y

config ARCH_SUSPEND_POSSIBLE
	def_bool y
	depends on ADB_PMU || PPC_EFIKA || PPC_LITE5200 || PPC_83xx || \
		   (PPC_85xx && !PPC_E500MC) || PPC_86xx || PPC_PSERIES \
		   || 44x

config ARCH_SUSPEND_NONZERO_CPU
	def_bool y
	depends on PPC_POWERNV || PPC_PSERIES

config ARCH_HAS_ADD_PAGES
	def_bool y
	depends on ARCH_ENABLE_MEMORY_HOTPLUG

config PPC_DCR_NATIVE
	bool

config PPC_DCR_MMIO
	bool

config PPC_DCR
	bool
	depends on PPC_DCR_NATIVE || PPC_DCR_MMIO
	default y

config PPC_PCI_OF_BUS_MAP
	bool "Use pci_to_OF_bus_map (deprecated)"
	depends on PPC32
	depends on PPC_PMAC || PPC_CHRP
	help
	  This option uses pci_to_OF_bus_map to map OF nodes to PCI devices, which
	  restricts the system to only having 256 PCI buses. On CHRP it also causes
	  the "pci-OF-bus-map" property to be created in the device tree.

	  If unsure, say "N".

config PPC_PCI_BUS_NUM_DOMAIN_DEPENDENT
	depends on PPC32
	depends on !PPC_PCI_OF_BUS_MAP
	bool "Assign PCI bus numbers from zero individually for each PCI domain"
	default y
	help
	  By default on PPC32 were PCI bus numbers unique across all PCI domains.
	  So system could have only 256 PCI buses independently of available
	  PCI domains. When this option is enabled then PCI bus numbers are
	  PCI domain dependent and each PCI controller on own domain can have
	  256 PCI buses, like it is on other Linux architectures.

config PPC_OF_PLATFORM_PCI
	bool
	depends on PCI
	depends on PPC64 # not supported on 32 bits yet

config ARCH_SUPPORTS_UPROBES
	def_bool y

config PPC_ADV_DEBUG_REGS
	bool
	depends on BOOKE
	default y

config PPC_ADV_DEBUG_IACS
	int
	depends on PPC_ADV_DEBUG_REGS
	default 4 if 44x
	default 2

config PPC_ADV_DEBUG_DACS
	int
	depends on PPC_ADV_DEBUG_REGS
	default 2

config PPC_ADV_DEBUG_DVCS
	int
	depends on PPC_ADV_DEBUG_REGS
	default 2 if 44x
	default 0

config PPC_ADV_DEBUG_DAC_RANGE
	bool
	depends on PPC_ADV_DEBUG_REGS && 44x
	default y

config PPC_DAWR
	bool

config PGTABLE_LEVELS
	int
	default 2 if !PPC64
	default 4

source "arch/powerpc/sysdev/Kconfig"
source "arch/powerpc/platforms/Kconfig"

menu "Kernel options"

config HIGHMEM
	bool "High memory support"
	depends on PPC32
	select KMAP_LOCAL

source "kernel/Kconfig.hz"

config MATH_EMULATION
	bool "Math emulation"
	depends on 44x || PPC_8xx || PPC_MPC832x || BOOKE || PPC_MICROWATT
	select PPC_FPU_REGS
	help
	  Some PowerPC chips designed for embedded applications do not have
	  a floating-point unit and therefore do not implement the
	  floating-point instructions in the PowerPC instruction set.  If you
	  say Y here, the kernel will include code to emulate a floating-point
	  unit, which will allow programs that use floating-point
	  instructions to run.

	  This is also useful to emulate missing (optional) instructions
	  such as fsqrt on cores that do have an FPU but do not implement
	  them (such as Freescale BookE).

choice
	prompt "Math emulation options"
	default MATH_EMULATION_FULL
	depends on MATH_EMULATION

config MATH_EMULATION_FULL
	bool "Emulate all the floating point instructions"
	help
	  Select this option will enable the kernel to support to emulate
	  all the floating point instructions. If your SoC doesn't have
	  a FPU, you should select this.

config MATH_EMULATION_HW_UNIMPLEMENTED
	bool "Just emulate the FPU unimplemented instructions"
	help
	  Select this if you know there does have a hardware FPU on your
	  SoC, but some floating point instructions are not implemented by that.

endchoice

config PPC_TRANSACTIONAL_MEM
	bool "Transactional Memory support for POWERPC"
	depends on PPC_BOOK3S_64
	depends on SMP
	select ALTIVEC
	select VSX
	help
	  Support user-mode Transactional Memory on POWERPC.

config PPC_UV
	bool "Ultravisor support"
	depends on KVM_BOOK3S_HV_POSSIBLE
	depends on DEVICE_PRIVATE
	default n
	help
	  This option paravirtualizes the kernel to run in POWER platforms that
	  supports the Protected Execution Facility (PEF). On such platforms,
	  the ultravisor firmware runs at a privilege level above the
	  hypervisor.

	  If unsure, say "N".

config LD_HEAD_STUB_CATCH
	bool "Reserve 256 bytes to cope with linker stubs in HEAD text" if EXPERT
	depends on PPC64
	help
	  Very large kernels can cause linker branch stubs to be generated by
	  code in head_64.S, which moves the head text sections out of their
	  specified location. This option can work around the problem.

	  If unsure, say "N".

config MPROFILE_KERNEL
	depends on PPC64_ELF_ABI_V2 && FUNCTION_TRACER
	def_bool $(success,$(srctree)/arch/powerpc/tools/gcc-check-mprofile-kernel.sh $(CC) -mlittle-endian) if CPU_LITTLE_ENDIAN
	def_bool $(success,$(srctree)/arch/powerpc/tools/gcc-check-mprofile-kernel.sh $(CC) -mbig-endian) if CPU_BIG_ENDIAN

config ARCH_USING_PATCHABLE_FUNCTION_ENTRY
	depends on FUNCTION_TRACER && (PPC32 || PPC64_ELF_ABI_V2)
	depends on $(cc-option,-fpatchable-function-entry=2)
	def_bool y if PPC32
	def_bool $(success,$(srctree)/arch/powerpc/tools/gcc-check-fpatchable-function-entry.sh $(CC) -mlittle-endian) if PPC64 && CPU_LITTLE_ENDIAN
	def_bool $(success,$(srctree)/arch/powerpc/tools/gcc-check-fpatchable-function-entry.sh $(CC) -mbig-endian) if PPC64 && CPU_BIG_ENDIAN

config HOTPLUG_CPU
	bool "Support for enabling/disabling CPUs"
	depends on SMP && (PPC_PSERIES || \
		PPC_PMAC || PPC_POWERNV || FSL_SOC_BOOKE)
	help
	  Say Y here to be able to disable and re-enable individual
	  CPUs at runtime on SMP machines.

	  Say N if you are unsure.

config INTERRUPT_SANITIZE_REGISTERS
	bool "Clear gprs on interrupt arrival"
	depends on PPC64 && ARCH_HAS_SYSCALL_WRAPPER
	default PPC_BOOK3E_64 || PPC_PSERIES || PPC_POWERNV
	help
	  Reduce the influence of user register state on interrupt handlers and
	  syscalls through clearing user state from registers before handling
	  the exception.

config PPC_QUEUED_SPINLOCKS
	bool "Queued spinlocks" if EXPERT
	depends on SMP
	default PPC_BOOK3S_64
	help
	  Say Y here to use queued spinlocks which give better scalability and
	  fairness on large SMP and NUMA systems without harming single threaded
	  performance.

config ARCH_CPU_PROBE_RELEASE
	def_bool y
	depends on HOTPLUG_CPU

config PPC64_SUPPORTS_MEMORY_FAILURE
	bool "Add support for memory hwpoison"
	depends on PPC_BOOK3S_64
	default "y" if PPC_POWERNV
	select ARCH_SUPPORTS_MEMORY_FAILURE

config ARCH_SUPPORTS_KEXEC
	def_bool PPC_BOOK3S || PPC_E500 || (44x && !SMP)

config ARCH_SUPPORTS_KEXEC_FILE
	def_bool PPC64

config ARCH_SUPPORTS_KEXEC_PURGATORY
	def_bool y

config ARCH_SELECTS_KEXEC_FILE
	def_bool y
	depends on KEXEC_FILE
	select KEXEC_ELF
	select HAVE_IMA_KEXEC if IMA

config PPC64_BIG_ENDIAN_ELF_ABI_V2
	# Option is available to BFD, but LLD does not support ELFv1 so this is
	# always true there.
	prompt "Build big-endian kernel using ELF ABI V2" if LD_IS_BFD && EXPERT
	def_bool y
	depends on PPC64 && CPU_BIG_ENDIAN
	depends on CC_HAS_ELFV2
	help
	  This builds the kernel image using the "Power Architecture 64-Bit ELF
	  V2 ABI Specification", which has a reduced stack overhead and faster
	  function calls. This internal kernel ABI option does not affect
          userspace compatibility.

	  The V2 ABI is standard for 64-bit little-endian, but for big-endian
	  it is less well tested by kernel and toolchain. However some distros
	  build userspace this way, and it can produce a functioning kernel.

config RELOCATABLE
	bool "Build a relocatable kernel"
	depends on PPC64 || (FLATMEM && (44x || PPC_85xx))
	select NONSTATIC_KERNEL
	help
	  This builds a kernel image that is capable of running at the
	  location the kernel is loaded at. For ppc32, there is no any
	  alignment restrictions, and this feature is a superset of
	  DYNAMIC_MEMSTART and hence overrides it. For ppc64, we should use
	  16k-aligned base address. The kernel is linked as a
	  position-independent executable (PIE) and contains dynamic relocations
	  which are processed early in the bootup process.

	  One use is for the kexec on panic case where the recovery kernel
	  must live at a different physical address than the primary
	  kernel.

	  Note: If CONFIG_RELOCATABLE=y, then the kernel runs from the address
	  it has been loaded at and the compile time physical addresses
	  CONFIG_PHYSICAL_START is ignored.  However CONFIG_PHYSICAL_START
	  setting can still be useful to bootwrappers that need to know the
	  load address of the kernel (eg. u-boot/mkimage).

config RANDOMIZE_BASE
	bool "Randomize the address of the kernel image"
	depends on PPC_85xx && FLATMEM
	depends on RELOCATABLE
	help
	  Randomizes the virtual address at which the kernel image is
	  loaded, as a security feature that deters exploit attempts
	  relying on knowledge of the location of kernel internals.

	  If unsure, say Y.

config RELOCATABLE_TEST
	bool "Test relocatable kernel"
	depends on (PPC64 && RELOCATABLE)
	help
	  This runs the relocatable kernel at the address it was initially
	  loaded at, which tends to be non-zero and therefore test the
	  relocation code.

config ARCH_SUPPORTS_CRASH_DUMP
	def_bool PPC64 || PPC_BOOK3S_32 || PPC_85xx || (44x && !SMP)

config ARCH_DEFAULT_CRASH_DUMP
	bool
	default y if !PPC_BOOK3S_32

config ARCH_SELECTS_CRASH_DUMP
	def_bool y
	depends on CRASH_DUMP
	select RELOCATABLE if PPC64 || 44x || PPC_85xx

config ARCH_SUPPORTS_CRASH_HOTPLUG
	def_bool y
	depends on PPC64

config FA_DUMP
	bool "Firmware-assisted dump"
	depends on CRASH_DUMP && PPC64 && (PPC_RTAS || PPC_POWERNV)
	help
	  A robust mechanism to get reliable kernel crash dump with
	  assistance from firmware. This approach does not use kexec,
	  instead firmware assists in booting the capture kernel
	  while preserving memory contents. Firmware-assisted dump
	  is meant to be a kdump replacement offering robustness and
	  speed not possible without system firmware assistance.

	  If unsure, say "y". Only special kernels like petitboot may
	  need to say "N" here.

config PRESERVE_FA_DUMP
	bool "Preserve Firmware-assisted dump"
	depends on PPC64 && PPC_POWERNV && !FA_DUMP
	help
	  On a kernel with FA_DUMP disabled, this option helps to preserve
	  crash data from a previously crash'ed kernel. Useful when the next
	  memory preserving kernel boot would process this crash data.
	  Petitboot kernel is the typical usecase for this option.

config OPAL_CORE
	bool "Export OPAL memory as /sys/firmware/opal/core"
	depends on PPC64 && PPC_POWERNV
	help
	  This option uses the MPIPL support in firmware to provide an
	  ELF core of OPAL memory after a crash. The ELF core is exported
	  as /sys/firmware/opal/core file which is helpful in debugging
	  OPAL crashes using GDB.

config IRQ_ALL_CPUS
	bool "Distribute interrupts on all CPUs by default"
	depends on SMP
	help
	  This option gives the kernel permission to distribute IRQs across
	  multiple CPUs.  Saying N here will route all IRQs to the first
	  CPU.  Generally saying Y is safe, although some problems have been
	  reported with SMP Power Macintoshes with this option enabled.

config NUMA
	bool "NUMA Memory Allocation and Scheduler Support"
	depends on PPC64 && SMP
	default y if PPC_PSERIES || PPC_POWERNV
	select USE_PERCPU_NUMA_NODE_ID
	help
	  Enable NUMA (Non-Uniform Memory Access) support.

	  The kernel will try to allocate memory used by a CPU on the
	  local memory controller of the CPU and add some more
	  NUMA awareness to the kernel.

config NODES_SHIFT
	int
	default "8" if PPC64
	default "4"
	depends on NUMA

config HAVE_MEMORYLESS_NODES
	def_bool y
	depends on NUMA

config ARCH_SELECT_MEMORY_MODEL
	def_bool y
	depends on PPC64

config ARCH_FLATMEM_ENABLE
	def_bool y
	depends on (PPC64 && !NUMA) || PPC32

config ARCH_SPARSEMEM_ENABLE
	def_bool y
	depends on PPC64
	select SPARSEMEM_VMEMMAP_ENABLE

config ARCH_SPARSEMEM_DEFAULT
	def_bool y
	depends on PPC_BOOK3S_64

config ILLEGAL_POINTER_VALUE
	hex
	# This is roughly half way between the top of user space and the bottom
	# of kernel space, which seems about as good as we can get.
	default 0x5deadbeef0000000 if PPC64
	default 0

config ARCH_MEMORY_PROBE
	def_bool y
	depends on MEMORY_HOTPLUG

choice
	prompt "Page size"
	default PPC_64K_PAGES if PPC_BOOK3S_64
	default PPC_4K_PAGES
	help
	  Select the kernel logical page size. Increasing the page size
	  will reduce software overhead at each page boundary, allow
	  hardware prefetch mechanisms to be more effective, and allow
	  larger dma transfers increasing IO efficiency and reducing
	  overhead. However the utilization of memory will increase.
	  For example, each cached file will using a multiple of the
	  page size to hold its contents and the difference between the
	  end of file and the end of page is wasted.

	  Some dedicated systems, such as software raid serving with
	  accelerated calculations, have shown significant increases.

	  If you configure a 64 bit kernel for 64k pages but the
	  processor does not support them, then the kernel will simulate
	  them with 4k pages, loading them on demand, but with the
	  reduced software overhead and larger internal fragmentation.
	  For the 32 bit kernel, a large page option will not be offered
	  unless it is supported by the configured processor.

	  If unsure, choose 4K_PAGES.

config PPC_4K_PAGES
	bool "4k page size"
	select HAVE_ARCH_SOFT_DIRTY if PPC_BOOK3S_64
	select HAVE_PAGE_SIZE_4KB

config PPC_16K_PAGES
	bool "16k page size"
	depends on 44x || PPC_8xx
	select HAVE_PAGE_SIZE_16KB

config PPC_64K_PAGES
	bool "64k page size"
	depends on 44x || PPC_BOOK3S_64
	select HAVE_ARCH_SOFT_DIRTY if PPC_BOOK3S_64
	select HAVE_PAGE_SIZE_64KB

config PPC_256K_PAGES
	bool "256k page size (Requires non-standard binutils settings)"
	depends on 44x && !PPC_47x
	select HAVE_PAGE_SIZE_256KB
	help
	  Make the page size 256k.

	  The kernel will only be able to run applications that have been
	  compiled with '-zmax-page-size' set to 256K (the default is 64K) using
	  binutils later than 2.17.50.0.3, or by patching the ELF_MAXPAGESIZE
	  definition from 0x10000 to 0x40000 in older versions.

endchoice

config THREAD_SHIFT
	int "Thread shift" if EXPERT
	range 13 15
	default "15" if PPC_256K_PAGES
	default "15" if PPC_PSERIES || PPC_POWERNV
	default "14" if PPC64
	default "13"
	help
	  Used to define the stack size. The default is almost always what you
	  want. Only change this if you know what you are doing.

config DATA_SHIFT_BOOL
	bool "Set custom data alignment"
	depends on ADVANCED_OPTIONS
	depends on STRICT_KERNEL_RWX || DEBUG_PAGEALLOC || KFENCE
	depends on PPC_BOOK3S_32 || (PPC_8xx && !PIN_TLB_DATA && !STRICT_KERNEL_RWX) || \
		   PPC_85xx
	help
	  This option allows you to set the kernel data alignment. When
	  RAM is mapped by blocks, the alignment needs to fit the size and
	  number of possible blocks. The default should be OK for most configs.

	  Say N here unless you know what you are doing.

config DATA_SHIFT
	int "Data shift" if DATA_SHIFT_BOOL
	default 24 if STRICT_KERNEL_RWX && PPC64
	range 17 28 if (STRICT_KERNEL_RWX || DEBUG_PAGEALLOC || KFENCE) && PPC_BOOK3S_32
	range 19 23 if (STRICT_KERNEL_RWX || DEBUG_PAGEALLOC || KFENCE) && PPC_8xx
	range 20 24 if (STRICT_KERNEL_RWX || DEBUG_PAGEALLOC || KFENCE) && PPC_85xx
	default 22 if STRICT_KERNEL_RWX && PPC_BOOK3S_32
	default 18 if (DEBUG_PAGEALLOC || KFENCE) && PPC_BOOK3S_32
	default 23 if STRICT_KERNEL_RWX && PPC_8xx
	default 23 if (DEBUG_PAGEALLOC || KFENCE) && PPC_8xx && PIN_TLB_DATA
	default 19 if (DEBUG_PAGEALLOC || KFENCE) && PPC_8xx
	default 24 if STRICT_KERNEL_RWX && PPC_85xx
	default PAGE_SHIFT
	help
	  On Book3S 32 (603+), DBATs are used to map kernel text and rodata RO.
	  Smaller is the alignment, greater is the number of necessary DBATs.

	  On 8xx, large pages (512kb or 8M) are used to map kernel linear
	  memory. Aligning to 8M reduces TLB misses as only 8M pages are used
	  in that case. If PIN_TLB is selected, it must be aligned to 8M as
	  8M pages will be pinned.

config ARCH_FORCE_MAX_ORDER
	int "Order of maximal physically contiguous allocations"
	range 7 8 if PPC64 && PPC_64K_PAGES
	default "8" if PPC64 && PPC_64K_PAGES
	range 12 12 if PPC64 && !PPC_64K_PAGES
	default "12" if PPC64 && !PPC_64K_PAGES
	range 8 10 if PPC32 && PPC_16K_PAGES
	default "8" if PPC32 && PPC_16K_PAGES
	range 6 10 if PPC32 && PPC_64K_PAGES
	default "6" if PPC32 && PPC_64K_PAGES
	range 4 10 if PPC32 && PPC_256K_PAGES
	default "4" if PPC32 && PPC_256K_PAGES
	range 10 12
	default "10"
	help
	  The kernel page allocator limits the size of maximal physically
	  contiguous allocations. The limit is called MAX_PAGE_ORDER and it
	  defines the maximal power of two of number of pages that can be
	  allocated as a single contiguous block. This option allows
	  overriding the default setting when ability to allocate very
	  large blocks of physically contiguous memory is required.

	  The page size is not necessarily 4KB.  For example, on 64-bit
	  systems, 64KB pages can be enabled via CONFIG_PPC_64K_PAGES.  Keep
	  this in mind when choosing a value for this option.

	  Don't change if unsure.

config PPC_SUBPAGE_PROT
	bool "Support setting protections for 4k subpages (subpage_prot syscall)"
	default n
	depends on PPC_64S_HASH_MMU && PPC_64K_PAGES
	help
	  This option adds support for system call to allow user programs
	  to set access permissions (read/write, readonly, or no access)
	  on the 4k subpages of each 64k page.

	  If unsure, say N here.

config PPC_PROT_SAO_LPAR
	bool "Support PROT_SAO mappings in LPARs"
	depends on PPC_BOOK3S_64
	help
	  This option adds support for PROT_SAO mappings from userspace
	  inside LPARs on supported CPUs.

	  This may cause issues when performing guest migration from
	  a CPU that supports SAO to one that does not.

	  If unsure, say N here.

config PPC_COPRO_BASE
	bool

config SCHED_SMT
	bool "SMT (Hyperthreading) scheduler support"
	depends on PPC64 && SMP
	help
	  SMT scheduler support improves the CPU scheduler's decision making
	  when dealing with POWER5 cpus at a cost of slightly increased
	  overhead in some places. If unsure say N here.

config PPC_DENORMALISATION
	bool "PowerPC denormalisation exception handling"
	depends on PPC_BOOK3S_64
	default "y" if PPC_POWERNV
	help
	  Add support for handling denormalisation of single precision
	  values.  Useful for bare metal only.  If unsure say Y here.

config CMDLINE
	string "Initial kernel command string"
	default ""
	help
	  On some platforms, there is currently no way for the boot loader to
	  pass arguments to the kernel. For these platforms, you can supply
	  some command-line options at build time by entering them here.  In
	  most cases you will need to specify the root device here.

choice
	prompt "Kernel command line type"
	depends on CMDLINE != ""
	default CMDLINE_FROM_BOOTLOADER

config CMDLINE_FROM_BOOTLOADER
	bool "Use bootloader kernel arguments if available"
	help
	  Uses the command-line options passed by the boot loader. If
	  the boot loader doesn't provide any, the default kernel command
	  string provided in CMDLINE will be used.

config CMDLINE_EXTEND
	bool "Extend bootloader kernel arguments"
	help
	  The command-line arguments provided by the boot loader will be
	  appended to the default kernel command string.

config CMDLINE_FORCE
	bool "Always use the default kernel command string"
	help
	  Always use the default kernel command string, even if the boot
	  loader passes other arguments to the kernel.
	  This is useful if you cannot or don't want to change the
	  command-line options your boot loader passes to the kernel.

endchoice

config EXTRA_TARGETS
	string "Additional default image types"
	help
	  List additional targets to be built by the bootwrapper here (separated
	  by spaces).  This is useful for targets that depend of device tree
	  files in the .dts directory.

	  Targets in this list will be build as part of the default build
	  target, or when the user does a 'make zImage' or a
	  'make zImage.initrd'.

	  If unsure, leave blank

config ARCH_WANTS_FREEZER_CONTROL
	def_bool y
	depends on ADB_PMU

source "kernel/power/Kconfig"

config PPC_MEM_KEYS
	prompt "PowerPC Memory Protection Keys"
	def_bool y
	depends on PPC_BOOK3S_64
	depends on PPC_64S_HASH_MMU
	select ARCH_USES_HIGH_VMA_FLAGS
	select ARCH_HAS_PKEYS
	help
	  Memory Protection Keys provides a mechanism for enforcing
	  page-based protections, but without requiring modification of the
	  page tables when an application changes protection domains.

	  For details, see Documentation/core-api/protection-keys.rst

	  If unsure, say y.

config PPC_SECURE_BOOT
	prompt "Enable secure boot support"
	bool
	depends on PPC_POWERNV || PPC_PSERIES
	depends on IMA_ARCH_POLICY
	imply IMA_SECURE_AND_OR_TRUSTED_BOOT
	select PSERIES_PLPKS if PPC_PSERIES
	help
	  Systems with firmware secure boot enabled need to define security
	  policies to extend secure boot to the OS. This config allows a user
	  to enable OS secure boot on systems that have firmware support for
	  it. If in doubt say N.

config PPC_SECVAR_SYSFS
	bool "Enable sysfs interface for POWER secure variables"
	default y
	depends on PPC_SECURE_BOOT
	depends on SYSFS
	help
	  POWER secure variables are managed and controlled by firmware.
	  These variables are exposed to userspace via sysfs to enable
	  read/write operations on these variables. Say Y if you have
	  secure boot enabled and want to expose variables to userspace.

endmenu

config ISA_DMA_API
	bool
	default PCI

menu "Bus options"

config ISA
	bool "Support for ISA-bus hardware"
	depends on PPC_CHRP
	select PPC_I8259
	help
	  Find out whether you have ISA slots on your motherboard.  ISA is the
	  name of a bus system, i.e. the way the CPU talks to the other stuff
	  inside your box.  If you have an Apple machine, say N here; if you
	  have an IBM RS/6000 or pSeries machine, say Y.  If you have an
	  embedded board, consult your board documentation.

config GENERIC_ISA_DMA
	bool
	depends on ISA_DMA_API
	default y

config PPC_INDIRECT_PCI
	bool
	depends on PCI
	default y if 44x

config SBUS
	bool

config FSL_SOC
	bool

config FSL_PCI
	bool
	select ARCH_HAS_DMA_SET_MASK
	select PPC_INDIRECT_PCI
	select PCI_QUIRKS

config FSL_PMC
	bool
	default y
	depends on SUSPEND && (PPC_85xx || PPC_86xx)
	help
	  Freescale MPC85xx/MPC86xx power management controller support
	  (suspend/resume). For MPC83xx see platforms/83xx/suspend.c

config PPC4xx_CPM
	bool
	default y
	depends on SUSPEND && 44x
	help
	  PPC4xx Clock Power Management (CPM) support (suspend/resume).
	  It also enables support for two different idle states (idle-wait
	  and idle-doze).

config FSL_LBC
	bool "Freescale Local Bus support"
	help
	  Enables reporting of errors from the Freescale local bus
	  controller.  Also contains some common code used by
	  drivers for specific local bus peripherals.

config FSL_GTM
	bool
	depends on PPC_83xx || QUICC_ENGINE || CPM2
	help
	  Freescale General-purpose Timers support

config FSL_RIO
	bool "Freescale Embedded SRIO Controller support"
	depends on RAPIDIO = y && HAVE_RAPIDIO
	default "n"
	help
	  Include support for RapidIO controller on Freescale embedded
	  processors (MPC8548, MPC8641, etc).

endmenu

config NONSTATIC_KERNEL
	bool

menu "Advanced setup"
	depends on PPC32

config ADVANCED_OPTIONS
	bool "Prompt for advanced kernel configuration options"
	help
	  This option will enable prompting for a variety of advanced kernel
	  configuration options.  These options can cause the kernel to not
	  work if they are set incorrectly, but can be used to optimize certain
	  aspects of kernel memory management.

	  Unless you know what you are doing, say N here.

comment "Default settings for advanced configuration options are used"
	depends on !ADVANCED_OPTIONS

config LOWMEM_SIZE_BOOL
	bool "Set maximum low memory"
	depends on ADVANCED_OPTIONS
	help
	  This option allows you to set the maximum amount of memory which
	  will be used as "low memory", that is, memory which the kernel can
	  access directly, without having to set up a kernel virtual mapping.
	  This can be useful in optimizing the layout of kernel virtual
	  memory.

	  Say N here unless you know what you are doing.

config LOWMEM_SIZE
	hex "Maximum low memory size (in bytes)" if LOWMEM_SIZE_BOOL
	default "0x30000000"

config LOWMEM_CAM_NUM_BOOL
	bool "Set number of CAMs to use to map low memory"
	depends on ADVANCED_OPTIONS && PPC_85xx
	help
	  This option allows you to set the maximum number of CAM slots that
	  will be used to map low memory.  There are a limited number of slots
	  available and even more limited number that will fit in the L1 MMU.
	  However, using more entries will allow mapping more low memory.  This
	  can be useful in optimizing the layout of kernel virtual memory.

	  Say N here unless you know what you are doing.

config LOWMEM_CAM_NUM
	depends on PPC_85xx
	int "Number of CAMs to use to map low memory" if LOWMEM_CAM_NUM_BOOL
	default 3 if !STRICT_KERNEL_RWX
	default 9 if DATA_SHIFT >= 24
	default 12 if DATA_SHIFT >= 22
	default 15

config DYNAMIC_MEMSTART
	bool "Enable page aligned dynamic load address for kernel"
	depends on ADVANCED_OPTIONS && FLATMEM && (PPC_85xx || 44x)
	select NONSTATIC_KERNEL
	help
	  This option enables the kernel to be loaded at any page aligned
	  physical address. The kernel creates a mapping from KERNELBASE to
	  the address where the kernel is loaded. The page size here implies
	  the TLB page size of the mapping for kernel on the particular platform.
	  Please refer to the init code for finding the TLB page size.

	  DYNAMIC_MEMSTART is an easy way of implementing pseudo-RELOCATABLE
	  kernel image, where the only restriction is the page aligned kernel
	  load address. When this option is enabled, the compile time physical
	  address CONFIG_PHYSICAL_START is ignored.

	  This option is overridden by CONFIG_RELOCATABLE

config PAGE_OFFSET_BOOL
	bool "Set custom page offset address"
	depends on ADVANCED_OPTIONS
	help
	  This option allows you to set the kernel virtual address at which
	  the kernel will map low memory.  This can be useful in optimizing
	  the virtual memory layout of the system.

	  Say N here unless you know what you are doing.

config PAGE_OFFSET
	hex "Virtual address of memory base" if PAGE_OFFSET_BOOL
	default "0xc0000000"

config KERNEL_START_BOOL
	bool "Set custom kernel base address"
	depends on ADVANCED_OPTIONS
	help
	  This option allows you to set the kernel virtual address at which
	  the kernel will be loaded.  Normally this should match PAGE_OFFSET
	  however there are times (like kdump) that one might not want them
	  to be the same.

	  Say N here unless you know what you are doing.

config KERNEL_START
	hex "Virtual address of kernel base" if KERNEL_START_BOOL
	default PAGE_OFFSET if PAGE_OFFSET_BOOL
	default "0xc2000000" if CRASH_DUMP && !NONSTATIC_KERNEL
	default "0xc0000000"

config PHYSICAL_START_BOOL
	bool "Set physical address where the kernel is loaded"
	depends on ADVANCED_OPTIONS && FLATMEM && PPC_85xx
	help
	  This gives the physical address where the kernel is loaded.

	  Say N here unless you know what you are doing.

config PHYSICAL_START
	hex "Physical address where the kernel is loaded" if PHYSICAL_START_BOOL
	default "0x02000000" if PPC_BOOK3S && CRASH_DUMP && !NONSTATIC_KERNEL
	default "0x00000000"

config PHYSICAL_ALIGN
	hex
	default "0x04000000" if PPC_85xx
	help
	  This value puts the alignment restrictions on physical address
	  where kernel is loaded and run from. Kernel is compiled for an
	  address which meets above alignment restriction.

config TASK_SIZE_BOOL
	bool "Set custom user task size"
	depends on ADVANCED_OPTIONS
	help
	  This option allows you to set the amount of virtual address space
	  allocated to user tasks.  This can be useful in optimizing the
	  virtual memory layout of the system.

	  Say N here unless you know what you are doing.

config TASK_SIZE
	hex "Size of user task space" if TASK_SIZE_BOOL
	default "0x80000000" if PPC_8xx
	default "0xb0000000" if PPC_BOOK3S_32
	default "0xc0000000"
endmenu

if PPC64
# This value must have zeroes in the bottom 60 bits otherwise lots will break
config PAGE_OFFSET
	hex
	default "0xc000000000000000"
config KERNEL_START
	hex
	default "0xc000000000000000"
config PHYSICAL_START
	hex
	default "0x00000000"
endif

config PPC_LIB_RHEAP
	bool

source "arch/powerpc/kvm/Kconfig"

source "kernel/livepatch/Kconfig"
