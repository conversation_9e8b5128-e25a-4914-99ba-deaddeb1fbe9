# SPDX-License-Identifier: GPL-2.0
config AMIGAON<PERSON>
	bool "Eyetech AmigaOne/MAI Teron"
	depends on PPC_BOOK3S_32 && BROKEN_ON_SMP
	select PPC_I8259
	select PPC_INDIRECT_PCI
	select PPC_UDBG_16550
	select FORCE_PC<PERSON>
	select NOT_COHERENT_CA<PERSON><PERSON>
	select CH<PERSON><PERSON>_CACHE_COHERENCY
	select DEFAULT_UIMAG<PERSON>
	select HAVE_PCSPKR_PLATFORM
	help
	Select AmigaOne for the following machines:
	- AmigaOne SE/Teron CX (G3 only)
	- AmigaOne XE/Teron PX
	- uA1/Teron mini
	  More information is available at:
	  <http://amigaone-linux.sourceforge.net/>.
