# SPDX-License-Identifier: GPL-2.0
config EMBEDDED6xx
	bool "Embedded 6xx/7xx/7xxx-based boards"
	depends on PPC_BOOK3S_32 && BROKEN_ON_SMP

config LINKSTATION
	bool "Linkstation / Kurobox(HG) from Buffalo"
	depends on EMBEDDED6xx
	select MPIC
	select FSL_SOC
	select PPC_UDBG_16550 if SERIAL_8250
	select DEFAULT_UIMAGE
	imply MPC10X_BRIDGE if PCI
	help
	  Select LINKSTATION if configuring for one of PPC- (MPC8241)
	  based NAS systems from Buffalo Technology. So far only
	  KuroboxHG has been tested. In the future classical Kurobox,
	  Linkstation-I HD-HLAN and HD-HGLAN versions, and PPC-based
	  Terastation systems should be supported too.

config STORCENTER
	bool "IOMEGA StorCenter"
	depends on EMBEDDED6xx
	select MPIC
	select FSL_SOC
	select PPC_UDBG_16550 if SERIAL_8250
	imply MPC10X_BRIDGE if PCI
	help
	  Select STORCENTER if configuring for the iomega StorCenter
	  with an 8241 CPU in it.

config PPC_HOLLY
	bool "PPC750GX/CL with TSI10x bridge (Hickory/Holly)"
	depends on EMBEDDED6xx
	select TSI108_BRIDGE
	select PPC_UDBG_16550
	help
	  Select PPC_HOLLY if configuring for an IBM 750GX/CL Eval
	  Board with TSI108/9 bridge (Hickory/Holly)

config MVME5100
	bool "Motorola/Emerson MVME5100"
	depends on EMBEDDED6xx
	select MPIC
	select FORCE_PCI
	select PPC_INDIRECT_PCI
	select PPC_I8259
	select PPC_HASH_MMU_NATIVE
	select PPC_UDBG_16550
	help
	  This option enables support for the Motorola (now Emerson) MVME5100
	  board.

config TSI108_BRIDGE
	bool
	select FORCE_PCI
	select MPIC
	select MPIC_WEIRD

config MPC10X_BRIDGE
	bool
	select PPC_INDIRECT_PCI

config GAMECUBE_COMMON
	bool

config USBGECKO_UDBG
	bool "USB Gecko udbg console for the Nintendo GameCube/Wii"
	depends on GAMECUBE_COMMON
	help
	  If you say yes to this option, support will be included for the
	  USB Gecko adapter as an udbg console.
	  The USB Gecko is a EXI to USB Serial converter that can be plugged
	  into a memcard slot in the Nintendo GameCube/Wii.

	  This driver bypasses the EXI layer completely.

	  If in doubt, say N here.

config GAMECUBE
	bool "Nintendo-GameCube"
	depends on EMBEDDED6xx
	select GAMECUBE_COMMON
	help
	  Select GAMECUBE if configuring for the Nintendo GameCube.
	  More information at: <http://gc-linux.sourceforge.net/>

config WII
	bool "Nintendo-Wii"
	depends on EMBEDDED6xx
	select GAMECUBE_COMMON
	help
	  Select WII if configuring for the Nintendo Wii.
	  More information at: <http://gc-linux.sourceforge.net/>
