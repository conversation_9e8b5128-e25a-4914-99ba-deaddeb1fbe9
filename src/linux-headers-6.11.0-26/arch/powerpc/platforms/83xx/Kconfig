# SPDX-License-Identifier: GPL-2.0
menuconfig PPC_83xx
	bool "83xx-based boards"
	depends on PPC_BOOK3S_32
	select PPC_UDBG_16550
	select HAVE_PCI
	select FSL_PCI if PCI
	select FSL_SOC
	select IPIC

if PPC_83xx

config MPC830x_RDB
	bool "Freescale MPC830x RDB and derivatives"
	select DEFAULT_UIMAGE
	select PPC_MPC831x
	select FSL_GTM
	help
	  This option enables support for the MPC8308 RDB and MPC8308 P1M boards.

config MPC831x_RDB
	bool "Freescale MPC831x RDB"
	select DEFAULT_UIMAGE
	select PPC_MPC831x
	help
	  This option enables support for the MPC8313 RDB and MPC8315 RDB boards.

config MPC832x_RDB
	bool "Freescale MPC832x RDB"
	select DEFAULT_UIMAGE
	select PPC_MPC832x
	help
	  This option enables support for the MPC8323 RDB board.

config MPC834x_ITX
	bool "Freescale MPC834x ITX"
	select DEFAULT_UIMAGE
	select PPC_MPC834x
	help
	  This option enables support for the MPC 834x ITX evaluation board.

	  Be aware that PCI initialization is the bootloader's
	  responsibility.

config MPC836x_RDK
	bool "Freescale/Logic MPC836x RDK"
	select DEFAULT_UIMAGE
	select FSL_GTM
	select FSL_LBC
	help
	  This option enables support for the MPC836x RDK Processor Board,
	  also known as ZOOM PowerQUICC Kit.

config MPC837x_RDB
	bool "Freescale MPC837x RDB/WLAN"
	select DEFAULT_UIMAGE
	select PPC_MPC837x
	help
	  This option enables support for the MPC837x RDB and WLAN Boards.

config ASP834x
	bool "Analogue & Micro ASP 834x"
	select PPC_MPC834x
	help
	  This enables support for the Analogue & Micro ASP 83xx
	  board.

config KMETER1
	bool "Keymile KMETER1"
	select DEFAULT_UIMAGE
	select QUICC_ENGINE
	help
	  This enables support for the Keymile KMETER1 board.


endif

# used for usb & gpio
config PPC_MPC831x
	bool

# used for math-emu
config PPC_MPC832x
	bool

# used for usb & gpio
config PPC_MPC834x
	bool

# used for usb & gpio
config PPC_MPC837x
	bool
