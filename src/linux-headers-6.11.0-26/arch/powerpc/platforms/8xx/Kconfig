# SPDX-License-Identifier: GPL-2.0
config CPM1
	bool
	select CPM

choice
	prompt "8xx Machine Type"
	depends on PPC_8xx
	default MPC885ADS

config MPC8XXFADS
	bool "FADS"

config MPC86XADS
	bool "MPC86XADS"
	select CPM1
	help
	  MPC86x Application Development System by Freescale Semiconductor.
	  The MPC86xADS is meant to serve as a platform for s/w and h/w
	  development around the MPC86X processor families.

config MPC885ADS
	bool "MPC885ADS"
	select CPM1
	select OF_DYNAMIC
	help
	  Freescale Semiconductor MPC885 Application Development System (ADS).
	  Also known as DUET.
	  The MPC885ADS is meant to serve as a platform for s/w and h/w
	  development around the MPC885 processor family.

config PPC_EP88XC
	bool "Embedded Planet EP88xC (a.k.a. CWH-PPC-885XN-VE)"
	select CPM1
	help
	  This enables support for the Embedded Planet EP88xC board.

	  This board is also resold by Freescale as the QUICCStart
	  MPC885 Evaluation System and/or the CWH-PPC-885XN-VE.

config PPC_ADDER875
	bool "Analogue & Micro Adder 875"
	select CPM1
	help
	  This enables support for the Analogue & Micro Adder 875
	  board.

config TQM8XX
	bool "TQM8XX"
	select CPM1
	help
	  support for the mpc8xx based boards from TQM.

endchoice

menu "Freescale Ethernet driver platform-specific options"
	depends on (FS_ENET && MPC885ADS)

	config MPC8xx_SECOND_ETH
	bool "Second Ethernet channel"
	depends on MPC885ADS
	default y
	help
	  This enables support for second Ethernet on MPC885ADS and MPC86xADS boards.
	  The latter will use SCC1, for 885ADS you can select it below.

	choice
		prompt "Second Ethernet channel"
		depends on MPC8xx_SECOND_ETH
		default MPC8xx_SECOND_ETH_FEC2

		config MPC8xx_SECOND_ETH_FEC2
		bool "FEC2"
		depends on MPC885ADS
		help
		  Enable FEC2 to serve as 2-nd Ethernet channel. Note that SMC2
		  (often 2-nd UART) will not work if this is enabled.

		config MPC8xx_SECOND_ETH_SCC3
		bool "SCC3"
		depends on MPC885ADS
		help
		  Enable SCC3 to serve as 2-nd Ethernet channel. Note that SMC1
		  (often 1-nd UART) will not work if this is enabled.

	endchoice

endmenu

#
# MPC8xx Communication options
#

menu "MPC8xx CPM Options"
	depends on PPC_8xx

# This doesn't really belong here, but it is convenient to ask
# 8xx specific questions.
comment "Generic MPC8xx Options"

config 8xx_GPIO
	bool "GPIO API Support"
	select GPIOLIB
	select OF_GPIO_MM_GPIOCHIP
	help
	  Saying Y here will cause the ports on an MPC8xx processor to be used
	  with the GPIO API.  If you say N here, the kernel needs less memory.

	  If in doubt, say Y here.

config 8xx_CPU15
	bool "CPU15 Silicon Errata"
	depends on !HUGETLB_PAGE
	default y
	help
	  This enables a workaround for erratum CPU15 on MPC8xx chips.
	  This bug can cause incorrect code execution under certain
	  circumstances.  This workaround adds some overhead (a TLB miss
	  every time execution crosses a page boundary), and you may wish
	  to disable it if you have worked around the bug in the compiler
	  (by not placing conditional branches or branches to LR or CTR
	  in the last word of a page, with a target of the last cache
	  line in the next page), or if you have used some other
	  workaround.

	  If in doubt, say Y here.

choice
	prompt "Microcode patch selection"
	default NO_UCODE_PATCH
	help
	  Help not implemented yet, coming soon.

config NO_UCODE_PATCH
	bool "None"

config USB_SOF_UCODE_PATCH
	bool "USB SOF patch"
	help
	  Help not implemented yet, coming soon.

config I2C_SPI_UCODE_PATCH
	bool "I2C/SPI relocation patch"
	help
	  Help not implemented yet, coming soon.

config I2C_SPI_SMC1_UCODE_PATCH
	bool "I2C/SPI/SMC1 relocation patch"
	help
	  Help not implemented yet, coming soon.

config SMC_UCODE_PATCH
	bool "SMC relocation patch"
	help
	  This microcode relocates SMC1 and SMC2 parameter RAMs at
	  offset 0x1ec0 and 0x1fc0 to allow extended parameter RAM
	  for SCC3 and SCC4.

endchoice

config UCODE_PATCH
	bool
	default y
	depends on !NO_UCODE_PATCH

menu "8xx advanced setup"
	depends on PPC_8xx

config PIN_TLB
	bool "Pinned Kernel TLBs"
	depends on ADVANCED_OPTIONS
	help
	  On the 8xx, we have 32 instruction TLBs and 32 data TLBs. In each
	  table 4 TLBs can be pinned.

	  It reduces the amount of usable TLBs to 28 (ie by 12%). That's the
	  reason why we make it selectable.

	  This option does nothing, it just activate the selection of what
	  to pin.

config PIN_TLB_DATA
	bool "Pinned TLB for DATA"
	depends on PIN_TLB
	default y
	help
	  This pins the first 32 Mbytes of memory with 8M pages.

config PIN_TLB_IMMR
	bool "Pinned TLB for IMMR"
	depends on PIN_TLB
	default y
	help
	  This pins the IMMR area with a 512kbytes page. In case
	  CONFIG_PIN_TLB_DATA is also selected, it will reduce
	  CONFIG_PIN_TLB_DATA to 24 Mbytes.

endmenu

endmenu
