# SPDX-License-Identifier: GPL-2.0
config PPC_VAS
	bool "IBM Virtual Accelerator Switchboard (VAS)"
	depends on (PPC_POWERNV || PPC_PSERIES) && PPC_64K_PAGES
	default y
	help
	  This enables support for IBM Virtual Accelerator Switchboard (VAS).

	  VAS devices are found in POWER9-based and later systems, they
	  provide access to accelerator coprocessors such as NX-GZIP and
	  NX-842. This config allows the kernel to use NX-842 accelerators,
	  and user-mode APIs for the NX-GZIP accelerator on POWER9 PowerNV
	  and POWER10 PowerVM platforms.

	  If unsure, say "N".
