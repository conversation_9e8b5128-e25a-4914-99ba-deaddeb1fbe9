# SPDX-License-Identifier: GPL-2.0
menuconfig PPC_82xx
	bool "82xx-based boards (PQ II)"
	depends on PPC_BOOK3S_32
	select FSL_SOC

if PPC_82xx

config EP8248E
	bool "Embedded Planet EP8248E (a.k.a. CWH-PPC-8248N-VE)"
	select <PERSON>M2
	select PPC_INDIRECT_PCI if PCI
	select PHYLIB if NETDEVICES
	select MDIO_BITBANG if PHYLIB
	help
	  This enables support for the Embedded Planet EP8248E board.

	  This board is also resold by Freescale as the QUICCStart
	  MPC8248 Evaluation System and/or the CWH-PPC-8248N-VE.

config MGCOGE
	bool "Keymile MGCOGE"
	select CPM2
	select PPC_INDIRECT_PCI if PCI
	help
	  This enables support for the Keymile MGCOGE board.

endif
