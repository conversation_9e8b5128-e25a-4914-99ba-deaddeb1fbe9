# SPDX-License-Identifier: GPL-2.0
menuconfig FSL_SOC_BOOKE
	bool "Freescale Book-E Machine Type"
	depends on PPC_E500
	select FSL_SOC
	select PPC_UDBG_16550
	select MPIC
	select HAVE_PCI
	select FSL_PCI if PCI
	select SERIAL_8250_EXTENDED if SERIAL_8250
	select SERIAL_8250_SHARE_IRQ if SERIAL_8250
	select FSL_CORENET_RCPM if PPC_E500MC
	default y

if FSL_SOC_BOOKE

if PPC32

config BSC9131_RDB
	bool "Freescale BSC9131RDB"
	select DEFAULT_UIMAGE
	help
	  This option enables support for the Freescale BSC9131RDB board.
	  The BSC9131 is a heterogeneous SoC containing an e500v2 powerpc and a
	  StarCore SC3850 DSP
	  Manufacturer : Freescale Semiconductor, Inc

config C293_PCIE
	  bool "Freescale C293PCIE"
	  select DEFAULT_UIMAGE
	  help
	  This option enables support for the C293PCIE board

config BSC9132_QDS
	bool "Freescale BSC9132QDS"
	select DEFAULT_UIMAGE
	help
	  This option enables support for the Freescale BSC9132 QDS board.
	  BSC9132 is a heterogeneous SoC containing dual e500v2 powerpc cores
	  and dual StarCore SC3850 DSP cores.
	  Manufacturer : Freescale Semiconductor, Inc

config MPC8540_ADS
	bool "Freescale MPC8540 ADS"
	select DEFAULT_UIMAGE
	help
	  This option enables support for the MPC 8540 ADS board

config MPC8560_ADS
	bool "Freescale MPC8560 ADS"
	select DEFAULT_UIMAGE
	select CPM2
	help
	  This option enables support for the MPC 8560 ADS board

config MPC85xx_CDS
	bool "Freescale MPC85xx CDS"
	select DEFAULT_UIMAGE
	select PPC_I8259
	select HAVE_RAPIDIO
	help
	  This option enables support for the MPC85xx CDS board

config MPC85xx_MDS
	bool "Freescale MPC8568 MDS / MPC8569 MDS / P1021 MDS"
	select DEFAULT_UIMAGE
	select PHYLIB if NETDEVICES
	select HAVE_RAPIDIO
	select SWIOTLB
	help
	  This option enables support for the MPC8568 MDS, MPC8569 MDS and P1021 MDS boards

config MPC8536_DS
	bool "Freescale MPC8536 DS"
	select DEFAULT_UIMAGE
	select SWIOTLB
	help
	  This option enables support for the MPC8536 DS board

config MPC85xx_DS
	bool "Freescale MPC8544 DS / MPC8572 DS"
	select PPC_I8259
	select DEFAULT_UIMAGE
	select FSL_ULI1575 if PCI
	select SWIOTLB
	help
	  This option enables support for the MPC8544 DS and MPC8572 DS boards

config MPC85xx_RDB
	bool "Freescale P102x MBG/UTM/RDB"
	select PPC_I8259
	select DEFAULT_UIMAGE
	select SWIOTLB
	help
	  This option enables support for the P1020 MBG PC, P1020 UTM PC,
	  P1020 RDB PC, P1020 RDB PD, P1020 RDB, P1021 RDB PC, P1024 RDB,
	  and P1025 RDB boards

config PPC_P2020
	bool "Freescale P2020"
	default y if MPC85xx_DS || MPC85xx_RDB
	select DEFAULT_UIMAGE
	select SWIOTLB
	imply PPC_I8259
	imply FSL_ULI1575 if PCI
	help
	  This option enables generic unified support for any board with the
	  Freescale P2020 processor.

	  For example: P2020 DS board, P2020 RDB board, P2020 RDB PC board or
	  CZ.NIC Turris 1.x boards.

config P1010_RDB
	bool "Freescale P1010 RDB"
	select DEFAULT_UIMAGE
	help
	  This option enables support for the P1010 RDB board

	  P1010RDB contains P1010Si, which provides CPU performance up to 800
	  MHz and 1600 DMIPS, additional functionality and faster interfaces
	  (DDR3/3L, SATA II, and PCI  Express).

config P1022_DS
	bool "Freescale P1022 DS"
	select DEFAULT_UIMAGE
	select SWIOTLB
	help
	  This option enables support for the Freescale P1022DS reference board.

config P1022_RDK
	bool "Freescale / iVeia P1022 RDK"
	select DEFAULT_UIMAGE
	help
	  This option enables support for the Freescale / iVeia P1022RDK
	  reference board.

config P1023_RDB
	bool "Freescale P1023 RDB"
	select DEFAULT_UIMAGE
	help
	  This option enables support for the P1023 RDB board.

config TWR_P102x
	bool "Freescale TWR-P102x"
	select DEFAULT_UIMAGE
	help
	  This option enables support for the TWR-P1025 board.

config SOCRATES
	bool "Socrates"
	select DEFAULT_UIMAGE
	help
	  This option enables support for the Socrates board.

config KSI8560
	bool "Emerson KSI8560"
	select DEFAULT_UIMAGE
	help
	  This option enables support for the Emerson KSI8560 board

config XES_MPC85xx
	bool "X-ES single-board computer"
	select DEFAULT_UIMAGE
	help
	  This option enables support for the various single-board
	  computers from Extreme Engineering Solutions (X-ES) based on
	  Freescale MPC85xx processors.
	  Manufacturer: Extreme Engineering Solutions, Inc.
	  URL: <https://www.xes-inc.com/>

config STX_GP3
	bool "Silicon Turnkey Express GP3"
	help
	  This option enables support for the Silicon Turnkey Express GP3
	  board.
	select CPM2
	select DEFAULT_UIMAGE

config TQM8540
	bool "TQ Components TQM8540"
	help
	  This option enables support for the TQ Components TQM8540 board.
	select DEFAULT_UIMAGE
	select TQM85xx

config TQM8541
	bool "TQ Components TQM8541"
	help
	  This option enables support for the TQ Components TQM8541 board.
	select DEFAULT_UIMAGE
	select TQM85xx
	select CPM2

config TQM8548
	bool "TQ Components TQM8548"
	help
	  This option enables support for the TQ Components TQM8548 board.
	select DEFAULT_UIMAGE
	select TQM85xx

config TQM8555
	bool "TQ Components TQM8555"
	help
	  This option enables support for the TQ Components TQM8555 board.
	select DEFAULT_UIMAGE
	select TQM85xx
	select CPM2

config TQM8560
	bool "TQ Components TQM8560"
	help
	  This option enables support for the TQ Components TQM8560 board.
	select DEFAULT_UIMAGE
	select TQM85xx
	select CPM2

config PPA8548
	bool "Prodrive PPA8548"
	help
	  This option enables support for the Prodrive PPA8548 board.
	select DEFAULT_UIMAGE
	select HAVE_RAPIDIO

config GE_IMP3A
	bool "GE Intelligent Platforms IMP3A"
	select DEFAULT_UIMAGE
	select SWIOTLB
	select MMIO_NVRAM
	select GPIOLIB
	select GE_FPGA
	help
	  This option enables support for the GE Intelligent Platforms IMP3A
	  board.

	  This board is a 3U CompactPCI Single Board Computer with a Freescale
	  P2020 processor.

config SGY_CTS1000
	tristate "Servergy CTS-1000 support"
	select GPIOLIB
	select OF_GPIO
	depends on CORENET_GENERIC
	help
	  Enable this to support functionality in Servergy's CTS-1000 systems.

config MVME2500
	bool "Artesyn MVME2500"
	select DEFAULT_UIMAGE
	help
	  This option enables support for the Emerson/Artesyn MVME2500 board.

endif # PPC32

config PPC_QEMU_E500
	bool "QEMU generic e500 platform"
	select DEFAULT_UIMAGE
	help
	  This option enables support for running as a QEMU guest using
	  QEMU's generic e500 machine.  This is not required if you're
	  using a QEMU machine that targets a specific board, such as
	  mpc8544ds.

	  Unlike most e500 boards that target a specific CPU, this
	  platform works with any e500-family CPU that QEMU supports.
	  Thus, you'll need to make sure CONFIG_PPC_E500MC is set or
	  unset based on the emulated CPU (or actual host CPU in the case
	  of KVM).

config CORENET_GENERIC
	bool "Freescale CoreNet Generic"
	select DEFAULT_UIMAGE
	select PPC_E500MC
	select PHYS_64BIT
	select SWIOTLB
	select GPIOLIB
	select GPIO_MPC8XXX
	select HAVE_RAPIDIO
	select PPC_EPAPR_HV_PIC
	help
	  This option enables support for the FSL CoreNet based boards.
	  For 32bit kernel, the following boards are supported:
	    P2041 RDB, P3041 DS, P4080 DS, kmcoge4, and OCA4080
	  For 64bit kernel, the following boards are supported:
	    T208x QDS/RDB, T4240 QDS/RDB and B4 QDS
	  The following boards are supported for both 32bit and 64bit kernel:
	    P5020 DS, P5040 DS, T102x QDS/RDB, T104x QDS/RDB

endif # FSL_SOC_BOOKE

config TQM85xx
	bool
