# SPDX-License-Identifier: GPL-2.0
menu "Platform support"

source "arch/powerpc/platforms/powernv/Kconfig"
source "arch/powerpc/platforms/pseries/Kconfig"
source "arch/powerpc/platforms/chrp/Kconfig"
source "arch/powerpc/platforms/512x/Kconfig"
source "arch/powerpc/platforms/52xx/Kconfig"
source "arch/powerpc/platforms/powermac/Kconfig"
source "arch/powerpc/platforms/maple/Kconfig"
source "arch/powerpc/platforms/pasemi/Kconfig"
source "arch/powerpc/platforms/ps3/Kconfig"
source "arch/powerpc/platforms/cell/Kconfig"
source "arch/powerpc/platforms/8xx/Kconfig"
source "arch/powerpc/platforms/82xx/Kconfig"
source "arch/powerpc/platforms/83xx/Kconfig"
source "arch/powerpc/platforms/85xx/Kconfig"
source "arch/powerpc/platforms/86xx/Kconfig"
source "arch/powerpc/platforms/embedded6xx/Kconfig"
source "arch/powerpc/platforms/44x/Kconfig"
source "arch/powerpc/platforms/amigaone/Kconfig"
source "arch/powerpc/platforms/book3s/Kconfig"
source "arch/powerpc/platforms/microwatt/Kconfig"

config KVM_GUEST
	bool "KVM Guest support"
	select EPAPR_PARAVIRT
	help
	  This option enables various optimizations for running under the KVM
	  hypervisor. Overhead for the kernel when not running inside KVM should
	  be minimal.

	  In case of doubt, say Y

config EPAPR_PARAVIRT
	bool "ePAPR para-virtualization support"
	help
	  Enables ePAPR para-virtualization support for guests.

	  In case of doubt, say Y

config PPC_HASH_MMU_NATIVE
	bool
	depends on PPC_BOOK3S
	help
	  Support for running natively on the hardware, i.e. without
	  a hypervisor. This option is not user-selectable but should
	  be selected by all platforms that need it.

config PPC_OF_BOOT_TRAMPOLINE
	bool "Support booting from Open Firmware or yaboot"
	depends on PPC_BOOK3S_32 || PPC64
	select RELOCATABLE if PPC64
	default y
	help
	  Support from booting from Open Firmware or yaboot using an
	  Open Firmware client interface. This enables the kernel to
	  communicate with open firmware to retrieve system information
	  such as the device tree.

	  In case of doubt, say Y

config PPC_DT_CPU_FTRS
	bool "Device-tree based CPU feature discovery & setup"
	depends on PPC_BOOK3S_64
	default y
	help
	  This enables code to use a new device tree binding for describing CPU
	  compatibility and features. Saying Y here will attempt to use the new
	  binding if the firmware provides it. Currently only the skiboot
	  firmware provides this binding.
	  If you're not sure say Y.

config UDBG_RTAS_CONSOLE
	bool "RTAS based debug console"
	depends on PPC_RTAS

config PPC_SMP_MUXED_IPI
	bool
	help
	  Select this option if your platform supports SMP and your
	  interrupt controller provides less than 4 interrupts to each
	  cpu.	This will enable the generic code to multiplex the 4
	  messages on to one ipi.

config IPIC
	bool

config MPIC
	bool

config MPIC_TIMER
	bool "MPIC Global Timer"
	depends on MPIC && FSL_SOC
	help
	  The MPIC global timer is a hardware timer inside the
	  Freescale PIC complying with OpenPIC standard. When the
	  specified interval times out, the hardware timer generates
	  an interrupt. The driver currently is only tested on fsl
	  chip, but it can potentially support other global timers
	  complying with the OpenPIC standard.

config FSL_MPIC_TIMER_WAKEUP
	tristate "Freescale MPIC global timer wakeup driver"
	depends on FSL_SOC &&  MPIC_TIMER && PM
	help
	  The driver provides a way to wake up the system by MPIC
	  timer.
	  e.g. "echo 5 > /sys/devices/system/mpic/timer_wakeup"

config PPC_EPAPR_HV_PIC
	bool
	select EPAPR_PARAVIRT

config MPIC_WEIRD
	bool

config MPIC_MSGR
	bool "MPIC message register support"
	depends on MPIC
	help
	  Enables support for the MPIC message registers.  These
	  registers are used for inter-processor communication.

config PPC_I8259
	bool

config U3_DART
	bool
	depends on PPC64

config PPC_RTAS
	bool

config RTAS_ERROR_LOGGING
	bool
	depends on PPC_RTAS

config PPC_RTAS_DAEMON
	bool
	depends on PPC_RTAS

config RTAS_PROC
	bool "Proc interface to RTAS"
	depends on PPC_RTAS && PROC_FS
	default y

config RTAS_FLASH
	tristate "Firmware flash interface"
	depends on PPC64 && RTAS_PROC

config MMIO_NVRAM
	bool

config MPIC_U3_HT_IRQS
	bool

config MPIC_BROKEN_REGREAD
	bool
	depends on MPIC
	help
	  This option enables a MPIC driver workaround for some chips
	  that have a bug that causes some interrupt source information
	  to not read back properly. It is safe to use on other chips as
	  well, but enabling it uses about 8KB of memory to keep copies
	  of the register contents in software.

config EEH
	bool
	depends on (PPC_POWERNV || PPC_PSERIES) && PCI
	default y

config PPC_MPC106
	bool

config PPC_970_NAP
	bool

config PPC_P7_NAP
	bool

config PPC_BOOK3S_IDLE
	def_bool y
	depends on (PPC_970_NAP || PPC_P7_NAP)

config PPC_INDIRECT_PIO
	bool
	select GENERIC_IOMAP

config PPC_INDIRECT_MMIO
	bool

config PPC_IO_WORKAROUNDS
	bool

source "drivers/cpufreq/Kconfig"

menu "CPUIdle driver"

source "drivers/cpuidle/Kconfig"

endmenu

config TAU
	bool "On-chip CPU temperature sensor support"
	depends on PPC_BOOK3S_32
	help
	  G3 and G4 processors have an on-chip temperature sensor called the
	  'Thermal Assist Unit (TAU)', which, in theory, can measure the on-die
	  temperature within 2-4 degrees Celsius. This option shows the current
	  on-die temperature in /proc/cpuinfo if the cpu supports it.

	  Unfortunately, this sensor is very inaccurate when uncalibrated, so
	  don't assume the cpu temp is actually what /proc/cpuinfo says it is.

config TAU_INT
	bool "Interrupt driven TAU driver (EXPERIMENTAL)"
	depends on TAU
	help
	  The TAU supports an interrupt driven mode which causes an interrupt
	  whenever the temperature goes out of range. This is the fastest way
	  to get notified the temp has exceeded a range. With this option off,
	  a timer is used to re-check the temperature periodically.

	  If in doubt, say N here.

config TAU_AVERAGE
	bool "Average high and low temp"
	depends on TAU
	help
	  The TAU hardware can compare the temperature to an upper and lower
	  bound.  The default behavior is to show both the upper and lower
	  bound in /proc/cpuinfo. If the range is large, the temperature is
	  either changing a lot, or the TAU hardware is broken (likely on some
	  G4's). If the range is small (around 4 degrees), the temperature is
	  relatively stable.  If you say Y here, a single temperature value,
	  halfway between the upper and lower bounds, will be reported in
	  /proc/cpuinfo.

	  If in doubt, say N here.

config QE_GPIO
	bool "QE GPIO support"
	depends on QUICC_ENGINE
	select GPIOLIB
	select OF_GPIO_MM_GPIOCHIP
	help
	  Say Y here if you're going to use hardware that connects to the
	  QE GPIOs.

config CPM2
	bool "Enable support for the CPM2 (Communications Processor Module)"
	depends on (FSL_SOC_BOOKE && PPC32) || PPC_82xx
	select CPM
	select HAVE_PCI
	select GPIOLIB
	select OF_GPIO_MM_GPIOCHIP
	help
	  The CPM2 (Communications Processor Module) is a coprocessor on
	  embedded CPUs made by Freescale.  Selecting this option means that
	  you wish to build a kernel for a machine with a CPM2 coprocessor
	  on it (826x, 827x, 8560).

config FSL_ULI1575
	bool "ULI1575 PCIe south bridge support"
	depends on FSL_SOC_BOOKE || PPC_86xx
	depends on PCI
	select FSL_PCI
	select GENERIC_ISA_DMA
	help
	  Supports for the ULI1575 PCIe south bridge that exists on some
	  Freescale reference boards. The boards all use the ULI in pretty
	  much the same way.

config CPM
	bool
	select GENERIC_ALLOCATOR

config OF_RTC
	bool
	help
	  Uses information from the OF or flattened device tree to instantiate
	  platform devices for direct mapped RTC chips like the DS1742 or DS1743.

config GEN_RTC
	bool "Use the platform RTC operations from user space"
	select RTC_CLASS
	select RTC_DRV_GENERIC
	help
	  This option provides backwards compatibility with the old gen_rtc.ko
	  module that was traditionally used for old PowerPC machines.
	  Platforms should migrate to enabling the RTC_DRV_GENERIC by hand
	  replacing their get_rtc_time/set_rtc_time callbacks with
	  a proper RTC device driver.

config MCU_MPC8349EMITX
	bool "MPC8349E-mITX MCU driver"
	depends on I2C=y && PPC_83xx
	select GPIOLIB
	help
	  Say Y here to enable soft power-off functionality on the Freescale
	  boards with the MPC8349E-mITX-compatible MCU chips. This driver will
	  also register MCU GPIOs with the generic GPIO API, so you'll able
	  to use MCU pins as GPIOs.

endmenu
