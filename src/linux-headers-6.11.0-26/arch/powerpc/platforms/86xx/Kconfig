# SPDX-License-Identifier: GPL-2.0
menuconfig PPC_86xx
	bool "86xx-based boards"
	depends on PPC_BOOK3S_32
	select FSL_SOC
	select ALTIVEC
	help
	  The Freescale E600 SoCs have 74xx cores.

if PPC_86xx

config GEF_PPC9A
	bool "GE PPC9A"
	select DEFAULT_UIMAGE
	select MMIO_NVRAM
	select GPIOLIB
	select GE_FPGA
	help
	  This option enables support for the GE PPC9A.

config GEF_SBC310
	bool "GE SBC310"
	select DEFAULT_UIMAGE
	select MMIO_NVRAM
	select GPIOLIB
	select GE_FPGA
	help
	  This option enables support for the GE SBC310.

config GEF_SBC610
	bool "GE SBC610"
	select DEFAULT_UIMAGE
	select MMIO_NVRAM
	select GPIOLIB
	select GE_FPGA
	select HAVE_RAPIDIO
	help
	  This option enables support for the GE SBC610.

config MVME7100
	bool "Artesyn MVME7100"
	help
	  This option enables support for the Emerson/Artesyn MVME7100 board.

endif

config MPC8641
	bool
	select HAV<PERSON>_<PERSON>I
	select FSL_PCI if PCI
	select PPC_UDBG_16550
	select MPIC
	default y if GEF_SBC610 || GEF_SBC310 || GEF_PPC9A \
			|| MVME7100
