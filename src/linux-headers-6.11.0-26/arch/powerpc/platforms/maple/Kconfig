# SPDX-License-Identifier: GPL-2.0
config PPC_MAPLE
	depends on PPC64 && PPC_BOOK3S && CPU_BIG_ENDIAN
	bool "Maple 970FX Evaluation Board"
	select FORCE_PCI
	select MPIC
	select U3_DART
	select MPIC_U3_HT_IRQS
	select GENERIC_TBSYNC
	select PPC_UDBG_16550
	select PPC_970_NAP
	select PPC_64S_HASH_MMU
	select PPC_HASH_MMU_NATIVE
	select PPC_RTAS
	select M<PERSON>O_NVRAM
	select ATA_NONSTANDARD if ATA
	help
	  This option enables support for the Maple 970FX Evaluation Board.
	  For more information, refer to <http://www.970eval.com>
