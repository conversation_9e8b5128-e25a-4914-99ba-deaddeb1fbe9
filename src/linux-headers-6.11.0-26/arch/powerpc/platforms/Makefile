# SPDX-License-Identifier: GPL-2.0

obj-$(CONFIG_FSL_ULI1575)	+= fsl_uli1575.o

obj-$(CONFIG_PPC_PMAC)		+= powermac/
obj-$(CONFIG_PPC_CHRP)		+= chrp/
obj-$(CONFIG_44x)		+= 44x/
obj-$(CONFIG_PPC_MPC512x)	+= 512x/
obj-$(CONFIG_PPC_MPC52xx)	+= 52xx/
obj-$(CONFIG_PPC_8xx)		+= 8xx/
obj-$(CONFIG_PPC_82xx)		+= 82xx/
obj-$(CONFIG_PPC_83xx)		+= 83xx/
obj-$(CONFIG_FSL_SOC_BOOKE)	+= 85xx/
obj-$(CONFIG_PPC_86xx)		+= 86xx/
obj-$(CONFIG_PPC_POWERNV)	+= powernv/
obj-$(CONFIG_PPC_PSERIES)	+= pseries/
obj-$(CONFIG_PPC_MAPLE)		+= maple/
obj-$(CONFIG_PPC_PASEMI)	+= pasemi/
obj-$(CONFIG_PPC_CELL)		+= cell/
obj-$(CONFIG_PPC_PS3)		+= ps3/
obj-$(CONFIG_EMBEDDED6xx)	+= embedded6xx/
obj-$(CONFIG_AMIGAONE)		+= amigaone/
obj-$(CONFIG_PPC_BOOK3S)	+= book3s/
obj-$(CONFIG_PPC_MICROWATT)	+= microwatt/
