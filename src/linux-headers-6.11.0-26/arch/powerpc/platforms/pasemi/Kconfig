# SPDX-License-Identifier: GPL-2.0
config PPC_PASEMI
	depends on PPC64 && PPC_BOOK3S && CPU_BIG_ENDIAN
	bool "PA Semi SoC-based platforms"
	select MPIC
	select FORCE_PCI
	select PPC_UDBG_16550
	select PPC_64S_HASH_M<PERSON>
	select PPC_HASH_MMU_NATIVE
	select MPIC_BROKEN_REGREAD
	help
	  This option enables support for PA Semi's PWRficient line
	  of SoC processors, including PA6T-1682M

menu "PA Semi PWRficient options"
	depends on PPC_PASEMI

config PPC_PASEMI_NEMO
	bool "Nemo motherboard Support"
	depends on PPC_PASEMI
	select PPC_I8259
	help
	  This option enables support for the 'Nemo' motherboard
	  used in A-Eons's Amigaone X1000. This consists of some
	  device tree patches and workarounds for the SB600 South
	  Bridge that provides SATA/USB/Audio.

config PPC_PASEMI_IOMMU
	bool "PA Semi IOMMU support"
	depends on PPC_PASEMI
	help
	  IOMMU support for PA Semi PWRficient

config PPC_PASEMI_IOMMU_DMA_FORCE
	bool "Force DMA engine to use IOMMU"
	depends on PPC_PASEMI_IOMMU
	help
	  This option forces the use of the IOMMU also for the
	  DMA engine. Otherwise the kernel will use it only when
	  running under a hypervisor.

	  If in doubt, say "N".

config PPC_PASEMI_MDIO
	depends on PHYLIB
	tristate "MDIO support via GPIO"
	default y
	help
	  Driver for MDIO via GPIO on PWRficient platforms

endmenu
