# SPDX-License-Identifier: GPL-2.0
config PPC_POWERNV
	depends on PPC64 && PPC_BOOK3S
	bool "IBM PowerNV (Non-Virtualized) platform support"
	select PPC_HASH_MMU_NATIVE if PPC_64S_HASH_MMU
	select PP<PERSON>_<PERSON><PERSON>
	select PPC_ICP_NATIVE
	select PPC_XIVE_NATIVE
	select PPC_P7_NAP
	select FORCE_PCI
	select PCI_MSI
	select EPAPR_BOOT
	select PPC_INDIRECT_PIO
	select PPC_UDBG_16550
	select CPU_FREQ
	select PPC_DOORBELL
	select MMU_NOTIFIER
	select FORCE_SMP
	select ARCH_SUPPORTS_PER_VMA_LOCK
	default y

config OPAL_PRD
	tristate "OPAL PRD driver"
	depends on PPC_POWERNV
	help
	  This enables the opal-prd driver, a facility to run processor
	  recovery diagnostics on OpenPower machines

config PPC_MEMTRACE
	bool "Enable runtime allocation of RAM for tracing"
	depends on PPC_POWERNV && MEMORY_HOTPLUG && CONTIG_ALLOC
	help
	  Enabling this option allows for runtime allocation of memory (RAM)
	  for hardware tracing.

config SCOM_<PERSON>BUGFS
	bool "Expose SCOM controllers via debugfs"
	depends on DEBUG_FS
