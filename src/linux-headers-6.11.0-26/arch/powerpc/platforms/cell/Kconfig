# SPDX-License-Identifier: GPL-2.0
config PPC_CELL
	select PPC_64S_HASH_MMU if PPC<PERSON>
	bool

config PPC_CELL_COMMON
	bool
	select PPC_CELL
	select PPC_DCR_<PERSON><PERSON><PERSON>
	select PPC_INDIRE<PERSON>_P<PERSON>
	select PPC_INDIRECT_<PERSON><PERSON><PERSON>
	select PPC_HASH_MMU_NATIVE
	select PPC_R<PERSON><PERSON>
	select IRQ_EDGE_EOI_HANDLER

config PPC_CELL_NATIVE
	bool
	select PPC_CELL_COMMON
	select MPIC
	select PPC_IO_WORKAROUNDS
	select IBM_EMAC_EMAC4 if IBM_EMAC
	select IBM_EMAC_RGMII if IBM_EMAC
	select IBM_EMAC_ZMII if IBM_EMAC #test only
	select IBM_EMAC_TAH if IBM_EMAC  #test only

config PPC_IBM_CELL_BLADE
	bool "IBM Cell Blade"
	depends on PPC64 && PPC_BOOK3S && CPU_BIG_ENDIAN
	select PPC_CELL_NATIVE
	select PPC_OF_PLATFORM_PCI
	select FORCE_PCI
	select M<PERSON><PERSON>_<PERSON>VR<PERSON>
	select PPC_UDBG_16550
	select UDBG_RTAS_CONSOLE

config AXON_MSI
	bool
	depends on PPC_IBM_CELL_BLADE && PCI_MSI
	select IRQ_DOMAIN_NOMAP
	default y

menu "Cell Broadband Engine options"
	depends on PPC_CELL

config SPU_FS
	tristate "SPU file system"
	default m
	depends on PPC_CELL
	depends on COREDUMP
	select SPU_BASE
	help
	  The SPU file system is used to access Synergistic Processing
	  Units on machines implementing the Broadband Processor
	  Architecture.

config SPU_BASE
	bool
	select PPC_COPRO_BASE

config CBE_RAS
	bool "RAS features for bare metal Cell BE"
	depends on PPC_CELL_NATIVE
	default y

config PPC_IBM_CELL_RESETBUTTON
	bool "IBM Cell Blade Pinhole reset button"
	depends on CBE_RAS && PPC_IBM_CELL_BLADE
	default y
	help
	  Support Pinhole Resetbutton on IBM Cell blades.
	  This adds a method to trigger system reset via front panel pinhole button.

config PPC_IBM_CELL_POWERBUTTON
	tristate "IBM Cell Blade power button"
	depends on PPC_IBM_CELL_BLADE && INPUT_EVDEV
	default y
	help
	  Support Powerbutton on IBM Cell blades.
	  This will enable the powerbutton as an input device.

config CBE_THERM
	tristate "CBE thermal support"
	default m
	depends on CBE_RAS && SPU_BASE

config PPC_PMI
	tristate
	default y
	depends on CPU_FREQ_CBE_PMI || PPC_IBM_CELL_POWERBUTTON
	help
	  PMI (Platform Management Interrupt) is a way to
	  communicate with the BMC (Baseboard Management Controller).
	  It is used in some IBM Cell blades.

config CBE_CPUFREQ_SPU_GOVERNOR
	tristate "CBE frequency scaling based on SPU usage"
	depends on SPU_FS && CPU_FREQ
	default m
	help
	  This governor checks for spu usage to adjust the cpu frequency.
	  If no spu is running on a given cpu, that cpu will be throttled to
	  the minimal possible frequency.

endmenu
