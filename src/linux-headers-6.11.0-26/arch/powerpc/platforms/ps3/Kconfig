# SPDX-License-Identifier: GPL-2.0
config PPC_PS3
	bool "Sony PS3"
	depends on PPC64 && PPC_BOOK3S && CPU_BIG_ENDIAN
	select PPC_CELL
	select USB_OHCI_LITTLE_ENDIAN
	select USB_OHCI_BIG_ENDIAN_MMIO
	select USB_EHCI_BIG_ENDIAN_MMIO
	select HAVE_PCI
	select IRQ_DOMAIN_NOMAP
	help
	  This option enables support for the Sony PS3 game console
	  and other platforms using the PS3 hypervisor.  Enabling this
	  option will allow building otheros.bld, a kernel image suitable
	  for programming into flash memory, and vmlinux, a kernel image
	  suitable for loading via kexec.

menu "PS3 Platform Options"
	depends on PPC_PS3

config PS3_ADVANCED
	depends on PPC_PS3
	bool "PS3 Advanced configuration options"
	help
	  This gives you access to some advanced options for the PS3. The
	  defaults should be fine for most users, but these options may make
	  it possible to better control the kernel configuration if you know
	  what you are doing.

	  Note that the answer to this question won't directly affect the
	  kernel: saying N will just cause the configurator to skip all
	  the questions about these options.

	  Most users should say N to this question.

config PS3_HTAB_SIZE
	depends on PPC_PS3
	int "PS3 Platform pagetable size" if PS3_ADVANCED
	range 18 20
	default 20
	help
	  This option is only for experts who may have the desire to fine
	  tune the pagetable size on their system.  The value here is
	  expressed as the log2 of the page table size.  Valid values are
	  18, 19, and 20, corresponding to 256KB, 512KB and 1MB respectively.

	  If unsure, choose the default (20) with the confidence that your
	  system will have optimal runtime performance.

config PS3_DYNAMIC_DMA
	depends on PPC_PS3
	bool "PS3 Platform dynamic DMA page table management"
	help
	  This option will enable kernel support to take advantage of the
	  per device dynamic DMA page table management provided by the Cell
	  processor's IO Controller.  This support incurs some runtime
	  overhead and also slightly increases kernel memory usage.  The
	  current implementation should be considered experimental.

	  This support is mainly for Linux kernel development.  If unsure,
	  say N.

config PS3_VUART
	depends on PPC_PS3
	tristate

config PS3_PS3AV
	depends on PPC_PS3
	tristate "PS3 AV settings driver" if PS3_ADVANCED
	select VIDEO
	select PS3_VUART
	default y
	help
	  Include support for the PS3 AV Settings driver.

	  This support is required for PS3 graphics and sound. In
	  general, all users will say Y or M.

config PS3_SYS_MANAGER
	depends on PPC_PS3
	tristate "PS3 System Manager driver" if PS3_ADVANCED
	select PS3_VUART
	default y
	help
	  Include support for the PS3 System Manager.

	  This support is required for PS3 system control.  In
	  general, all users will say Y or M.

config PS3_VERBOSE_RESULT
	bool "PS3 Verbose LV1 hypercall results" if PS3_ADVANCED
	depends on PPC_PS3
	help
	  Enables more verbose log messages for LV1 hypercall results.

	  If in doubt, say N here and reduce the size of the kernel by a
	  small amount.

config PS3_REPOSITORY_WRITE
	bool "PS3 Repository write support" if PS3_ADVANCED
	depends on PPC_PS3
	help
	  Enables support for writing to the PS3 System Repository.

	  This support is intended for bootloaders that need to store data
	  in the repository for later boot stages.

	  If in doubt, say N here and reduce the size of the kernel by a
	  small amount.

config PS3_STORAGE
	depends on PPC_PS3
	tristate

config PS3_DISK
	tristate "PS3 Disk Storage Driver"
	depends on PPC_PS3 && BLOCK
	select PS3_STORAGE
	help
	  Include support for the PS3 Disk Storage.

	  This support is required to access the PS3 hard disk.
	  In general, all users will say Y or M.

config PS3_ROM
	tristate "PS3 BD/DVD/CD-ROM Storage Driver"
	depends on PPC_PS3 && SCSI
	select PS3_STORAGE
	help
	  Include support for the PS3 ROM Storage.

	  This support is required to access the PS3 BD/DVD/CD-ROM drive.
	  In general, all users will say Y or M.
	  Also make sure to say Y or M to "SCSI CDROM support" later.

config PS3_FLASH
	tristate "PS3 FLASH ROM Storage Driver"
	depends on PPC_PS3
	select PS3_STORAGE
	help
	  Include support for the PS3 FLASH ROM Storage.

	  This support is required to access the PS3 FLASH ROM, which
	  contains the boot loader and some boot options.
	  In general, PS3 OtherOS users will say Y or M.

	  As this driver needs a fixed buffer of 256 KiB of memory, it can
	  be disabled on the kernel command line using "ps3flash=off", to
	  not allocate this fixed buffer.

config PS3_VRAM
	tristate "PS3 Video RAM Storage Driver"
	depends on FB_PS3=y && BLOCK && m
	help
	  This driver allows you to use excess PS3 video RAM as volatile
	  storage or system swap.

config PS3_LPM
	tristate "PS3 Logical Performance Monitor support"
	depends on PPC_PS3
	help
	  Include support for the PS3 Logical Performance Monitor.

	  This support is required to use the logical performance monitor
	  of the PS3's LV1 hypervisor.

	  If you intend to use the advanced performance monitoring and
	  profiling support of the Cell processor with programs like
	  perfmon2, then say Y or M, otherwise say N.

endmenu
