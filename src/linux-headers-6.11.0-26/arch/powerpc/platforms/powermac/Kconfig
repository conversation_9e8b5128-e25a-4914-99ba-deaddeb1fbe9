# SPDX-License-Identifier: GPL-2.0
config PPC_PMAC
	bool "Apple PowerMac based machines"
	depends on PPC_BOOK3S && CPU_BIG_ENDIAN
	select ADB_CUDA if POWER_RESET && ADB
	select MPIC
	select FORCE_PCI
	select PPC_INDIRECT_PCI if <PERSON><PERSON><PERSON>
	select PPC_MPC106 if <PERSON><PERSON><PERSON>
	select PPC_64S_HASH_MMU if <PERSON><PERSON><PERSON>
	select PPC_HASH_MMU_NATIVE
	select ZONE_DMA if PP<PERSON><PERSON>
	default y

config PPC_PMAC64
	bool
	depends on PPC_PMAC && PPC64
	select MPIC
	select U3_DART
	select MPIC_U3_HT_IRQS
	select GENERIC_TBSYNC
	select PPC_970_NAP
	default y

config PPC_PMAC32_PSURGE
	bool "Support for powersurge upgrade cards" if EXPERT
	depends on SMP && PPC32 && PPC_PMAC
	select PPC_SMP_MUXED_IPI
	select IRQ_DOMAIN_NOMAP
	default y
	help
	  The powersurge cpu boards can be used in the generation
	  of powermacs that have a socket for an upgradeable cpu card,
	  including the 7500, 8500, 9500, 9600.  Support exists for
	  both dual and quad socket upgrade cards.
