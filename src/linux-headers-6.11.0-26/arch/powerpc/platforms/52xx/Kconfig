# SPDX-License-Identifier: GPL-2.0
config PPC_MPC52xx
	bool "52xx-based boards"
	depends on PPC_BOOK3S_32
	select COMMON_CL<PERSON>
	select HAVE_PCI

config PPC_MPC5200_SIMPLE
	bool "Generic support for simple MPC5200 based boards"
	depends on PPC_MPC52xx
	select DEFAULT_UIMAGE
	help
	  This option enables support for a simple MPC52xx based boards which
	  do not need a custom platform specific setup. Such boards are
	  supported assuming the following:

	  - GPIO pins are configured by the firmware,
	  - CDM configuration (clocking) is setup correctly by firmware,
	  - if the 'fsl,has-wdt' property is present in one of the
	    gpt nodes, then it is safe to use such gpt to reset the board,
	  - PCI is supported if enabled in the kernel configuration
	    and if there is a PCI bus node defined in the device tree.

	  Boards that are compatible with this generic platform support
	  are:
	     intercontrol,digsy-mtc
	     phytec,pcm030
	     phytec,pcm032
	     promess,motionpro
	     schindler,cm5200
	     tqc,tqm5200

config PPC_EFIKA
	bool "bPlan Efika 5k2. MPC5200B based computer"
	depends on PPC_MPC52xx
	select PPC_RTAS
	select PPC_HASH_MMU_NATIVE

config PPC_LITE5200
	bool "Freescale Lite5200 Eval Board"
	depends on PPC_MPC52xx
	select DEFAULT_UIMAGE

config PPC_MEDIA5200
	bool "Freescale Media5200 Eval Board"
	depends on PPC_MPC52xx
	select DEFAULT_UIMAGE

config PPC_MPC5200_BUGFIX
	bool "MPC5200 (L25R) bugfix support"
	depends on PPC_MPC52xx
	help
	  Enable workarounds for original MPC5200 errata.  This is not required
	  for MPC5200B based boards.

	  It is safe to say 'Y' here
