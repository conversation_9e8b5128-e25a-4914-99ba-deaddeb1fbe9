# Copyright © 2016 IBM Corporation

# This program is free software; you can redistribute it and/or
# modify it under the terms of the GNU General Public License
# as published by the Free Software Foundation; either version
# 2 of the License, or (at your option) any later version.

# This script checks the head of a vmlinux for linker stubs that
# break our placement of fixed-location code for 64-bit.

# based on relocs_check.pl
# Copyright © 2009 IBM Corporation

# NOTE!
#
# If the build dies here, it's likely code in head_64.S/exception-64*.S or
# nearby, is branching to labels it can't reach directly, which results in the
# linker inserting branch stubs. This can move code around in ways that break
# the fixed section calculations (head-64.h). To debug this, disassemble the
# vmlinux and look for branch stubs (long_branch, plt_branch, etc.) in the
# fixed section region (0 - 0x8000ish). Check what code is calling those stubs,
# and perhaps change so a direct branch can reach.
#
# A ".linker_stub_catch" section is used to catch some stubs generated by
# early .text code, which tend to get placed at the start of the section.
# If there are too many such stubs, they can overflow this section. Expanding
# it may help (or reducing the number of stub branches).
#
# Linker stubs use the TOC pointer, so even if fixed section code could
# tolerate them being inserted into head code, they can't be allowed in low
# level entry code (boot, interrupt vectors, etc) until r2 is set up. This
# could cause the kernel to die in early boot.

# Allow for verbose output
if [ "$V" = "1" ]; then
	set -x
fi

if [ $# -lt 2 ]; then
	echo "$0 [path to nm] [path to vmlinux]" 1>&2
	exit 1
fi

# Have Kbuild supply the path to nm so we handle cross compilation.
nm="$1"
vmlinux="$2"

# gcc-4.6-era toolchain make _stext an A (absolute) symbol rather than T
$nm "$vmlinux" | grep -e " [TA] _stext$" -e " t start_first_256B$" -e " a text_start$" -e " t start_text$" > .tmp_symbols.txt


vma=$(grep -e " [TA] _stext$" .tmp_symbols.txt | cut -d' ' -f1)

expected_start_head_addr="$vma"

start_head_addr=$(grep " t start_first_256B$" .tmp_symbols.txt | cut -d' ' -f1)

if [ "$start_head_addr" != "$expected_start_head_addr" ]; then
	echo "ERROR: head code starts at $start_head_addr, should be $expected_start_head_addr" 1>&2
	echo "ERROR: try to enable LD_HEAD_STUB_CATCH config option" 1>&2
	echo "ERROR: see comments in arch/powerpc/tools/head_check.sh" 1>&2

	exit 1
fi

top_vma=$(echo "$vma" | cut -d'0' -f1)

expected_start_text_addr=$(grep " a text_start$" .tmp_symbols.txt | cut -d' ' -f1 | sed "s/^0/$top_vma/")

start_text_addr=$(grep " t start_text$" .tmp_symbols.txt | cut -d' ' -f1)

if [ "$start_text_addr" != "$expected_start_text_addr" ]; then
	echo "ERROR: start_text address is $start_text_addr, should be $expected_start_text_addr" 1>&2
	echo "ERROR: try to enable LD_HEAD_STUB_CATCH config option" 1>&2
	echo "ERROR: see comments in arch/powerpc/tools/head_check.sh" 1>&2

	exit 1
fi

rm -f .tmp_symbols.txt
