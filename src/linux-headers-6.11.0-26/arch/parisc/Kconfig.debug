# SPDX-License-Identifier: GPL-2.0
#
config LIGHTWEIGHT_SPINLOCK_CHECK
	bool "Enable lightweight spinlock checks"
	depends on DEBUG_KERNEL && SMP && !DEBUG_SPINLOCK
	default y
	help
	  Add checks with low performance impact to the spinlock functions
	  to catch memory overwrites at runtime. For more advanced
	  spinlock debugging you should choose the DEBUG_SPINLOCK option
	  which will detect unitialized spinlocks too.
	  If unsure say Y here.

config TLB_PTLOCK
	bool "Use page table locks in TLB fault handler"
	depends on DEBUG_KERNEL && SMP
	default n
	help
	  Select this option to enable page table locking in the TLB
	  fault handler. This ensures that page table entries are
	  updated consistently on SMP machines at the expense of some
	  loss in performance.

