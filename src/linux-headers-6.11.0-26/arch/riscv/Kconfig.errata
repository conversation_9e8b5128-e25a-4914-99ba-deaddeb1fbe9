menu "CPU errata selection"

config ERRATA_ANDES
	bool "Andes AX45MP errata"
	depends on RISCV_ALTERNATIVE && RISCV_SBI
	help
	  All Andes errata Kconfig depend on this Kconfig. Disabling
	  this Kconfig will disable all Andes errata. Please say "Y"
	  here if your platform uses Andes CPU cores.

	  Otherwise, please say "N" here to avoid unnecessary overhead.

config ERRATA_ANDES_CMO
	bool "Apply Andes cache management errata"
	depends on ERRATA_ANDES && ARCH_R9A07G043
	select RISCV_DMA_NONCOHERENT
	default y
	help
	  This will apply the cache management errata to handle the
	  non-standard handling on non-coherent operations on Andes cores.

	  If you don't know what to do here, say "Y".

config ERRATA_SIFIVE
	bool "SiFive errata"
	depends on RISCV_ALTERNATIVE
	help
	  All SiFive errata Kconfig depend on this Kconfig. Disabling
	  this Kconfig will disable all SiFive errata. Please say "Y"
	  here if your platform uses SiFive CPU cores.

	  Otherwise, please say "N" here to avoid unnecessary overhead.

config ERRATA_SIFIVE_CIP_453
	bool "Apply SiFive errata CIP-453"
	depends on ERRATA_SIFIVE && 64BIT
	default y
	help
	  This will apply the SiFive CIP-453 errata to add sign extension
	  to the $badaddr when exception type is instruction page fault
	  and instruction access fault.

	  If you don't know what to do here, say "Y".

config ERRATA_SIFIVE_CIP_1200
	bool "Apply SiFive errata CIP-1200"
	depends on ERRATA_SIFIVE && 64BIT
	default y
	help
	  This will apply the SiFive CIP-1200 errata to repalce all
	  "sfence.vma addr" with "sfence.vma" to ensure that the addr
	  has been flushed from TLB.

	  If you don't know what to do here, say "Y".

config ERRATA_STARFIVE_JH7100
	bool "StarFive JH7100 support"
	depends on ARCH_STARFIVE
	depends on !DMA_DIRECT_REMAP
	depends on NONPORTABLE
	select DMA_GLOBAL_POOL
	select RISCV_DMA_NONCOHERENT
	select RISCV_NONSTANDARD_CACHE_OPS
	select SIFIVE_CCACHE
	default n
	help
	  The StarFive JH7100 was a test chip for the JH7110 and has
	  caches that are non-coherent with respect to peripheral DMAs.
	  It was designed before the Zicbom extension so needs non-standard
	  cache operations through the SiFive cache controller.

	  Say "Y" if you want to support the BeagleV Starlight and/or
	  StarFive VisionFive V1 boards.

config ERRATA_THEAD
	bool "T-HEAD errata"
	depends on RISCV_ALTERNATIVE
	help
	  All T-HEAD errata Kconfig depend on this Kconfig. Disabling
	  this Kconfig will disable all T-HEAD errata. Please say "Y"
	  here if your platform uses T-HEAD CPU cores.

	  Otherwise, please say "N" here to avoid unnecessary overhead.

config ERRATA_THEAD_MAE
	bool "Apply T-Head's memory attribute extension (XTheadMae) errata"
	depends on ERRATA_THEAD && 64BIT && MMU
	select RISCV_ALTERNATIVE_EARLY
	default y
	help
	  This will apply the memory attribute extension errata to handle the
	  non-standard PTE utilization on T-Head SoCs (XTheadMae).

	  If you don't know what to do here, say "Y".

config ERRATA_THEAD_CMO
	bool "Apply T-Head cache management errata"
	depends on ERRATA_THEAD && MMU
	select DMA_DIRECT_REMAP
	select RISCV_DMA_NONCOHERENT
	select RISCV_NONSTANDARD_CACHE_OPS
	default y
	help
	  This will apply the cache management errata to handle the
	  non-standard handling on non-coherent operations on T-Head SoCs.

	  If you don't know what to do here, say "Y".

config ERRATA_THEAD_PMU
	bool "Apply T-Head PMU errata"
	depends on ERRATA_THEAD && RISCV_PMU_SBI
	default y
	help
	  The T-Head C9xx cores implement a PMU overflow extension very
	  similar to the core SSCOFPMF extension.

	  This will apply the overflow errata to handle the non-standard
	  behaviour via the regular SBI PMU driver and interface.

	  If you don't know what to do here, say "Y".

endmenu # "CPU errata selection"
