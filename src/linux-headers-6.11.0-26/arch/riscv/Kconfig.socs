menu "SoC selection"

config ARCH_<PERSON>CROCHIP_P<PERSON><PERSON><PERSON><PERSON>
	def_bool ARCH_MICROCHIP

config ARCH_MICROCHIP
	bool "Microchip SoCs"
	help
	  This enables support for Microchip SoC platforms.

config ARCH_RENESAS
	bool "Renesas RISC-V SoCs"
	help
	  This enables support for the RISC-V based Renesas SoCs.

config ARCH_SIFIVE
	bool "SiFive SoCs"
	select ERRATA_SIFIVE if !XIP_KERNEL
	help
	  This enables support for SiFive SoC platform hardware.

config ARCH_SOPHGO
	bool "Sophgo SoCs"
	help
	  This enables support for Sophgo SoC platform hardware.

config ARCH_STARFIVE
	def_bool SOC_STARFIVE

config SOC_STARFIVE
	bool "StarFive SoCs"
	select PINCTRL
	select RESET_CONTROLLER
	select ARM_AMBA
	help
	  This enables support for StarFive SoC platform hardware.

config ARCH_SUNXI
	bool "Allwinner sun20i SoCs"
	depends on MMU && !XIP_KERNEL
	select ERRATA_THEAD
	select SUN4I_TIMER
	help
	  This enables support for Allwinner sun20i platform hardware,
	  including boards based on the D1 and D1s SoCs.

config ARCH_THEAD
	bool "T-HEAD RISC-V SoCs"
	depends on MMU && !XIP_KERNEL
	select ERRATA_THEAD
	help
	  This enables support for the RISC-V based T-HEAD SoCs.

config ARCH_VIRT
	bool "QEMU Virt Machine"
	select CLINT_TIMER if RISCV_M_MODE
	select POWER_RESET
	select POWER_RESET_SYSCON
	select POWER_RESET_SYSCON_POWEROFF
	select GOLDFISH
	select RTC_DRV_GOLDFISH if RTC_CLASS
	select PM_GENERIC_DOMAINS if PM
	select PM_GENERIC_DOMAINS_OF if PM && OF
	select RISCV_SBI_CPUIDLE if CPU_IDLE && RISCV_SBI
	help
	  This enables support for QEMU Virt Machine.

config ARCH_CANAAN
	bool "Canaan Kendryte SoC"
	help
	  This enables support for Canaan Kendryte series SoC platform hardware.

config SOC_CANAAN_K210
	bool "Canaan Kendryte K210 SoC"
	depends on !MMU && ARCH_CANAAN
	select CLINT_TIMER if RISCV_M_MODE
	select ARCH_HAS_RESET_CONTROLLER
	select PINCTRL
	select COMMON_CLK
	help
	  This enables support for Canaan Kendryte K210 SoC platform hardware.

endmenu # "SoC selection"
