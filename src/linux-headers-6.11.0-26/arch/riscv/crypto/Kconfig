# SPDX-License-Identifier: GPL-2.0

menu "Accelerated Cryptographic Algorithms for CPU (riscv)"

config CRYPTO_AES_RISCV64
	tristate "Ciphers: AES, modes: ECB, CBC, CTS, CTR, XTS"
	depends on 64BIT && RISCV_ISA_V && TOOLCHAIN_HAS_VECTOR_CRYPTO
	select CRYPTO_ALGAPI
	select CRYPTO_LIB_AES
	select CRYPTO_SKCIPHER
	help
	  Block cipher: AES cipher algorithms
	  Length-preserving ciphers: AES with ECB, CBC, CTS, CTR, XTS

	  Architecture: riscv64 using:
	  - Zvkned vector crypto extension
	  - Zvbb vector extension (XTS)
	  - Zvkb vector crypto extension (CTR)
	  - Zvkg vector crypto extension (XTS)

config CRYPTO_CHACHA_RISCV64
	tristate "Ciphers: ChaCha"
	depends on 64BIT && RISCV_ISA_V && TOOLCHAIN_HAS_VECTOR_CRYPTO
	select CRYPTO_SKCIPHER
	select CRYPTO_LIB_CHACHA_GENERIC
	help
	  Length-preserving ciphers: ChaCha20 stream cipher algorithm

	  Architecture: riscv64 using:
	  - Zvkb vector crypto extension

config CRYPTO_GHASH_RISCV64
	tristate "Hash functions: GHASH"
	depends on 64BIT && RISCV_ISA_V && TOOLCHAIN_HAS_VECTOR_CRYPTO
	select CRYPTO_GCM
	help
	  GCM GHASH function (NIST SP 800-38D)

	  Architecture: riscv64 using:
	  - Zvkg vector crypto extension

config CRYPTO_SHA256_RISCV64
	tristate "Hash functions: SHA-224 and SHA-256"
	depends on 64BIT && RISCV_ISA_V && TOOLCHAIN_HAS_VECTOR_CRYPTO
	select CRYPTO_SHA256
	help
	  SHA-224 and SHA-256 secure hash algorithm (FIPS 180)

	  Architecture: riscv64 using:
	  - Zvknha or Zvknhb vector crypto extensions
	  - Zvkb vector crypto extension

config CRYPTO_SHA512_RISCV64
	tristate "Hash functions: SHA-384 and SHA-512"
	depends on 64BIT && RISCV_ISA_V && TOOLCHAIN_HAS_VECTOR_CRYPTO
	select CRYPTO_SHA512
	help
	  SHA-384 and SHA-512 secure hash algorithm (FIPS 180)

	  Architecture: riscv64 using:
	  - Zvknhb vector crypto extension
	  - Zvkb vector crypto extension

config CRYPTO_SM3_RISCV64
	tristate "Hash functions: SM3 (ShangMi 3)"
	depends on 64BIT && RISCV_ISA_V && TOOLCHAIN_HAS_VECTOR_CRYPTO
	select CRYPTO_HASH
	select CRYPTO_SM3
	help
	  SM3 (ShangMi 3) secure hash function (OSCCA GM/T 0004-2012)

	  Architecture: riscv64 using:
	  - Zvksh vector crypto extension
	  - Zvkb vector crypto extension

config CRYPTO_SM4_RISCV64
	tristate "Ciphers: SM4 (ShangMi 4)"
	depends on 64BIT && RISCV_ISA_V && TOOLCHAIN_HAS_VECTOR_CRYPTO
	select CRYPTO_ALGAPI
	select CRYPTO_SM4
	help
	  SM4 block cipher algorithm (OSCCA GB/T 32907-2016,
	  ISO/IEC 18033-3:2010/Amd 1:2021)

	  SM4 (GBT.32907-2016) is a cryptographic standard issued by the
	  Organization of State Commercial Administration of China (OSCCA)
	  as an authorized cryptographic algorithm for use within China.

	  Architecture: riscv64 using:
	  - Zvksed vector crypto extension
	  - Zvkb vector crypto extension

endmenu
