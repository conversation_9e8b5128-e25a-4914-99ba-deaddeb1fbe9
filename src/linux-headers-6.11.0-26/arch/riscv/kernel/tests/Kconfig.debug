# SPDX-License-Identifier: GPL-2.0-only
menu "arch/riscv/kernel Testing and Coverage"

config AS_HAS_ULEB128
	def_bool $(as-instr,.reloc label$(comma) R_RISCV_SET_ULEB128$(comma) 127\n.reloc label$(comma) R_RISCV_SUB_ULEB128$(comma) 127\nlabel:\n.word 0)

menuconfig RUNTIME_KERNEL_TESTING_MENU
       bool "arch/riscv/kernel runtime Testing"
       default y
       help
         Enable riscv kernel runtime testing.

if RUNTIME_KERNEL_TESTING_MENU

config RISCV_MODULE_LINKING_KUNIT
       bool "KUnit test riscv module linking at runtime" if !KUNIT_ALL_TESTS
       depends on KUNIT
       default KUNIT_ALL_TESTS
       help
         Enable this option to test riscv module linking at boot. This will
	 enable a module called "test_module_linking".

         KUnit tests run during boot and output the results to the debug log
         in TAP format (http://testanything.org/). Only useful for kernel devs
         running the KUnit test harness, and not intended for inclusion into a
         production build.

         For more information on KUnit and unit tests in general please refer
         to the KUnit documentation in Documentation/dev-tools/kunit/.

         If unsure, say N.

endif # RUNTIME_TESTING_MENU

endmenu # "arch/riscv/kernel runtime Testing"
