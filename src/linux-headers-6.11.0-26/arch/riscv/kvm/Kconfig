# SPDX-License-Identifier: GPL-2.0
#
# KVM configuration
#

source "virt/kvm/Kconfig"

menuconfig VIRTUALIZATION
	bool "Virtualization"
	help
	  Say Y here to get to see options for using your Linux host to run
	  other operating systems inside virtual machines (guests).
	  This option alone does not add any kernel code.

	  If you say N, all options in this submenu will be skipped and
	  disabled.

if VIRTUALIZATION

config KVM
	tristate "Kernel-based Virtual Machine (KVM) support (EXPERIMENTAL)"
	depends on RISCV_SBI && MMU
	select HAVE_KVM_IRQCHIP
	select HAVE_KVM_IRQ_ROUTING
	select HAVE_KVM_MSI
	select HAVE_KVM_VCPU_ASYNC_IOCTL
	select HAVE_KVM_READONLY_MEM
	select KVM_COMMON
	select KVM_GENERIC_DIRTYLOG_READ_PROTECT
	select KVM_GENERIC_HARDWARE_ENABLING
	select KVM_MMIO
	select KVM_XFER_TO_GUEST_W<PERSON><PERSON>
	select KVM_GENERIC_MMU_NOTIFIER
	select SCHED_INFO
	help
	  Support hosting virtualized guest machines.

	  If unsure, say N.

endif # VIRTUALIZATION
