#!/bin/sh
#
# This file is subject to the terms and conditions of the GNU General Public
# License.  See the file "COPYING" in the main directory of this archive
# for more details.
#
# Copyright (C) 1995 by <PERSON><PERSON>
#
# Adapted from code in arch/i386/boot/Makefile by <PERSON><PERSON>
# Adapted from code in arch/i386/boot/install.sh by <PERSON>
#
# "make install" script for the RISC-V Linux port
#
# Arguments:
#   $1 - kernel version
#   $2 - kernel image file
#   $3 - kernel map file
#   $4 - default install path (blank if root directory)

set -e

case "${2##*/}" in
# Compressed install
Image.*|vmlinuz.efi)
  echo "Installing compressed kernel"
  base=vmlinuz
  ;;
# Normal install
*)
  echo "Installing normal kernel"
  base=vmlinux
  ;;
esac

if [ -f $4/$base-$1 ]; then
  mv $4/$base-$1 $4/$base-$1.old
fi
cat $2 > $4/$base-$1

# Install system map file
if [ -f $4/System.map-$1 ]; then
  mv $4/System.map-$1 $4/System.map-$1.old
fi
cp $3 $4/System.map-$1
