menu "Vendor extensions"

config RISCV_ISA_VENDOR_EXT
	bool

menu "Andes"
config RISCV_ISA_VENDOR_EXT_ANDES
	bool "Andes vendor extension support"
	select RISCV_ISA_VENDOR_EXT
	default y
	help
	  Say <PERSON> here if you want to disable all Andes vendor extension
	  support. This will cause any Andes vendor extensions that are
	  requested by hardware probing to be ignored.

	  If you don't know what to do here, say Y.
endmenu

endmenu
