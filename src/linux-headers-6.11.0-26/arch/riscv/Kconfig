# SPDX-License-Identifier: GPL-2.0-only
#
# For a description of the syntax of this configuration file,
# see Documentation/kbuild/kconfig-language.rst.
#

config 64BIT
	bool

config 32BIT
	bool

config R<PERSON><PERSON>V
	def_bool y
	select ACPI_GENERIC_GSI if <PERSON>PI
	select ACPI_PPTT if <PERSON>PI
	select ACPI_REDUCED_HARDWARE_ONLY if ACPI
	select ACPI_SPCR_TABLE if <PERSON>PI
	select ARCH_DMA_DEFAULT_COHERENT
	select ARCH_ENABLE_HUGEPAGE_MIGRATION if HUGETLB_PAGE && MIGRATION
	select ARCH_ENABLE_MEMORY_HOTPLUG if SPARSEMEM_VMEMMAP
	select ARCH_ENABLE_MEMORY_HOTREMOVE if MEMORY_HOTPLUG
	select ARCH_ENABLE_SPLIT_PMD_PTLOCK if PGTABLE_LEVELS > 2
	select ARCH_ENABLE_THP_MIGRATION if TRANSPARENT_HUGEPAGE
	select ARCH_HAS_BINFMT_FLAT
	select ARCH_HAS_CURRENT_STACK_POINTER
	select ARCH_HAS_DEBUG_VIRTUAL if M<PERSON>
	select ARCH_HAS_DEBUG_VM_PGTABLE
	select ARCH_HAS_DEBUG_WX
	select ARCH_HAS_FAST_MULTIPLIER
	select ARCH_HAS_FORTIFY_SOURCE
	select ARCH_HAS_GCOV_PROFILE_ALL
	select ARCH_HAS_GIGANTIC_PAGE
	select ARCH_HAS_KCOV
	select ARCH_HAS_KERNEL_FPU_SUPPORT if 64BIT && FPU
	select ARCH_HAS_MEMBARRIER_CALLBACKS
	select ARCH_HAS_MEMBARRIER_SYNC_CORE
	select ARCH_HAS_MMIOWB
	select ARCH_HAS_NON_OVERLAPPING_ADDRESS_SPACE
	select ARCH_HAS_PMEM_API
	select ARCH_HAS_PREPARE_SYNC_CORE_CMD
	select ARCH_HAS_PTE_DEVMAP if 64BIT && MMU
	select ARCH_HAS_PTE_SPECIAL
	select ARCH_HAS_SET_DIRECT_MAP if MMU
	select ARCH_HAS_SET_MEMORY if MMU
	select ARCH_HAS_STRICT_KERNEL_RWX if MMU && !XIP_KERNEL
	select ARCH_HAS_STRICT_MODULE_RWX if MMU && !XIP_KERNEL
	select ARCH_HAS_SYNC_CORE_BEFORE_USERMODE
	select ARCH_HAS_SYSCALL_WRAPPER
	select ARCH_HAS_TICK_BROADCAST if GENERIC_CLOCKEVENTS_BROADCAST
	select ARCH_HAS_UBSAN
	select ARCH_HAS_VDSO_DATA
	select ARCH_KEEP_MEMBLOCK if ACPI
	select ARCH_MHP_MEMMAP_ON_MEMORY_ENABLE	if 64BIT && MMU
	select ARCH_OPTIONAL_KERNEL_RWX if ARCH_HAS_STRICT_KERNEL_RWX
	select ARCH_OPTIONAL_KERNEL_RWX_DEFAULT
	select ARCH_STACKWALK
	select ARCH_SUPPORTS_ATOMIC_RMW
	select ARCH_SUPPORTS_CFI_CLANG
	select ARCH_SUPPORTS_DEBUG_PAGEALLOC if MMU
	select ARCH_SUPPORTS_HUGETLBFS if MMU
	# LLD >= 14: https://github.com/llvm/llvm-project/issues/50505
	select ARCH_SUPPORTS_LTO_CLANG if LLD_VERSION >= 140000
	select ARCH_SUPPORTS_LTO_CLANG_THIN if LLD_VERSION >= 140000
	select ARCH_SUPPORTS_PAGE_TABLE_CHECK if MMU
	select ARCH_SUPPORTS_PER_VMA_LOCK if MMU
	select ARCH_SUPPORTS_SHADOW_CALL_STACK if HAVE_SHADOW_CALL_STACK
	select ARCH_USE_CMPXCHG_LOCKREF if 64BIT
	select ARCH_USE_MEMTEST
	select ARCH_USE_QUEUED_RWLOCKS
	select ARCH_USES_CFI_TRAPS if CFI_CLANG
	select ARCH_WANT_BATCHED_UNMAP_TLB_FLUSH if MMU
	select ARCH_WANT_DEFAULT_TOPDOWN_MMAP_LAYOUT if MMU
	select ARCH_WANT_FRAME_POINTERS
	select ARCH_WANT_GENERAL_HUGETLB if !RISCV_ISA_SVNAPOT
	select ARCH_WANT_HUGE_PMD_SHARE if 64BIT
	select ARCH_WANT_LD_ORPHAN_WARN if !XIP_KERNEL
	select ARCH_WANT_OPTIMIZE_DAX_VMEMMAP
	select ARCH_WANT_OPTIMIZE_HUGETLB_VMEMMAP
	select ARCH_WANTS_NO_INSTR
	select ARCH_WANTS_THP_SWAP if HAVE_ARCH_TRANSPARENT_HUGEPAGE
	select BINFMT_FLAT_NO_DATA_START_OFFSET if !MMU
	select BUILDTIME_TABLE_SORT if MMU
	select CLINT_TIMER if RISCV_M_MODE
	select CLONE_BACKWARDS
	select COMMON_CLK
	select CPU_PM if CPU_IDLE || HIBERNATION || SUSPEND
	select EDAC_SUPPORT
	select FRAME_POINTER if PERF_EVENTS || (FUNCTION_TRACER && !DYNAMIC_FTRACE)
	select FTRACE_MCOUNT_USE_PATCHABLE_FUNCTION_ENTRY if DYNAMIC_FTRACE
	select GENERIC_ARCH_TOPOLOGY
	select GENERIC_ATOMIC64 if !64BIT
	select GENERIC_CLOCKEVENTS_BROADCAST if SMP
	select GENERIC_CPU_DEVICES
	select GENERIC_EARLY_IOREMAP
	select GENERIC_ENTRY
	select GENERIC_GETTIMEOFDAY if HAVE_GENERIC_VDSO
	select GENERIC_IDLE_POLL_SETUP
	select GENERIC_IOREMAP if MMU
	select GENERIC_IRQ_IPI if SMP
	select GENERIC_IRQ_IPI_MUX if SMP
	select GENERIC_IRQ_MULTI_HANDLER
	select GENERIC_IRQ_SHOW
	select GENERIC_IRQ_SHOW_LEVEL
	select GENERIC_LIB_DEVMEM_IS_ALLOWED
	select GENERIC_PCI_IOMAP
	select GENERIC_PTDUMP if MMU
	select GENERIC_SCHED_CLOCK
	select GENERIC_SMP_IDLE_THREAD
	select GENERIC_TIME_VSYSCALL if MMU && 64BIT
	select GENERIC_VDSO_TIME_NS if HAVE_GENERIC_VDSO
	select HARDIRQS_SW_RESEND
	select HAS_IOPORT if MMU
	select HAVE_ARCH_AUDITSYSCALL
	select HAVE_ARCH_HUGE_VMALLOC if HAVE_ARCH_HUGE_VMAP
	select HAVE_ARCH_HUGE_VMAP if MMU && 64BIT
	select HAVE_ARCH_JUMP_LABEL if !XIP_KERNEL
	select HAVE_ARCH_JUMP_LABEL_RELATIVE if !XIP_KERNEL
	select HAVE_ARCH_KASAN if MMU && 64BIT
	select HAVE_ARCH_KASAN_VMALLOC if MMU && 64BIT
	select HAVE_ARCH_KFENCE if MMU && 64BIT
	select HAVE_ARCH_KGDB if !XIP_KERNEL
	select HAVE_ARCH_KGDB_QXFER_PKT
	select HAVE_ARCH_MMAP_RND_BITS if MMU
	select HAVE_ARCH_MMAP_RND_COMPAT_BITS if COMPAT
	select HAVE_ARCH_RANDOMIZE_KSTACK_OFFSET
	select HAVE_ARCH_SECCOMP_FILTER
	select HAVE_ARCH_STACKLEAK
	select HAVE_ARCH_THREAD_STRUCT_WHITELIST
	select HAVE_ARCH_TRACEHOOK
	select HAVE_ARCH_TRANSPARENT_HUGEPAGE if 64BIT && MMU
	select HAVE_ARCH_USERFAULTFD_MINOR if 64BIT && USERFAULTFD
	select HAVE_ARCH_VMAP_STACK if MMU && 64BIT
	select HAVE_ASM_MODVERSIONS
	select HAVE_CONTEXT_TRACKING_USER
	select HAVE_DEBUG_KMEMLEAK
	select HAVE_DMA_CONTIGUOUS if MMU
	select HAVE_DYNAMIC_FTRACE if !XIP_KERNEL && MMU && (CLANG_SUPPORTS_DYNAMIC_FTRACE || GCC_SUPPORTS_DYNAMIC_FTRACE)
	select HAVE_DYNAMIC_FTRACE_WITH_DIRECT_CALLS
	select HAVE_DYNAMIC_FTRACE_WITH_ARGS if HAVE_DYNAMIC_FTRACE
	select HAVE_FTRACE_MCOUNT_RECORD if !XIP_KERNEL
	select HAVE_FUNCTION_GRAPH_TRACER
	select HAVE_FUNCTION_GRAPH_RETVAL if HAVE_FUNCTION_GRAPH_TRACER
	select HAVE_FUNCTION_TRACER if !XIP_KERNEL && !PREEMPTION
	select HAVE_EBPF_JIT if MMU
	select HAVE_GUP_FAST if MMU
	select HAVE_FUNCTION_ARG_ACCESS_API
	select HAVE_FUNCTION_ERROR_INJECTION
	select HAVE_GCC_PLUGINS
	select HAVE_GENERIC_VDSO if MMU && 64BIT
	select HAVE_IRQ_TIME_ACCOUNTING
	select HAVE_KERNEL_BZIP2 if !XIP_KERNEL && !EFI_ZBOOT
	select HAVE_KERNEL_GZIP if !XIP_KERNEL && !EFI_ZBOOT
	select HAVE_KERNEL_LZ4 if !XIP_KERNEL && !EFI_ZBOOT
	select HAVE_KERNEL_LZMA if !XIP_KERNEL && !EFI_ZBOOT
	select HAVE_KERNEL_LZO if !XIP_KERNEL && !EFI_ZBOOT
	select HAVE_KERNEL_UNCOMPRESSED if !XIP_KERNEL && !EFI_ZBOOT
	select HAVE_KERNEL_ZSTD if !XIP_KERNEL && !EFI_ZBOOT
	select HAVE_KPROBES if !XIP_KERNEL
	select HAVE_KRETPROBES if !XIP_KERNEL
	# https://github.com/ClangBuiltLinux/linux/issues/1881
	select HAVE_LD_DEAD_CODE_DATA_ELIMINATION if !LD_IS_LLD
	select HAVE_MOVE_PMD
	select HAVE_MOVE_PUD
	select HAVE_PAGE_SIZE_4KB
	select HAVE_PCI
	select HAVE_PERF_EVENTS
	select HAVE_PERF_REGS
	select HAVE_PERF_USER_STACK_DUMP
	select HAVE_POSIX_CPU_TIMERS_TASK_WORK
	select HAVE_PREEMPT_DYNAMIC_KEY if !XIP_KERNEL
	select HAVE_REGS_AND_STACK_ACCESS_API
	select HAVE_RETHOOK if !XIP_KERNEL
	select HAVE_RSEQ
	select HAVE_RUST if 64BIT && CC_IS_CLANG
	select HAVE_SAMPLE_FTRACE_DIRECT
	select HAVE_SAMPLE_FTRACE_DIRECT_MULTI
	select HAVE_STACKPROTECTOR
	select HAVE_SYSCALL_TRACEPOINTS
	select HOTPLUG_CORE_SYNC_DEAD if HOTPLUG_CPU
	select IRQ_DOMAIN
	select IRQ_FORCED_THREADING
	select KASAN_VMALLOC if KASAN
	select LOCK_MM_AND_FIND_VMA
	select MMU_GATHER_RCU_TABLE_FREE if SMP && MMU
	select MODULES_USE_ELF_RELA if MODULES
	select OF
	select OF_EARLY_FLATTREE
	select OF_IRQ
	select PCI_DOMAINS_GENERIC if PCI
	select PCI_MSI if PCI
	select RISCV_ALTERNATIVE if !XIP_KERNEL
	select RISCV_APLIC
	select RISCV_IMSIC
	select RISCV_INTC
	select RISCV_TIMER if RISCV_SBI
	select SIFIVE_PLIC
	select SPARSE_IRQ
	select SYSCTL_EXCEPTION_TRACE
	select THREAD_INFO_IN_TASK
	select TRACE_IRQFLAGS_SUPPORT
	select UACCESS_MEMCPY if !MMU
	select ZONE_DMA32 if 64BIT

config CLANG_SUPPORTS_DYNAMIC_FTRACE
	def_bool CC_IS_CLANG
	# https://github.com/ClangBuiltLinux/linux/issues/1817
	depends on AS_IS_GNU || (AS_IS_LLVM && (LD_IS_LLD || LD_VERSION >= 23600))

config GCC_SUPPORTS_DYNAMIC_FTRACE
	def_bool CC_IS_GCC
	depends on $(cc-option,-fpatchable-function-entry=8)

config HAVE_SHADOW_CALL_STACK
	def_bool $(cc-option,-fsanitize=shadow-call-stack)
	# https://github.com/riscv-non-isa/riscv-elf-psabi-doc/commit/a484e843e6eeb51f0cb7b8819e50da6d2444d769
	depends on $(ld-option,--no-relax-gp)

config RISCV_USE_LINKER_RELAXATION
	def_bool y
	# https://github.com/llvm/llvm-project/commit/6611d58f5bbcbec77262d392e2923e1d680f6985
	depends on !LD_IS_LLD || LLD_VERSION >= 150000

# https://github.com/llvm/llvm-project/commit/bbc0f99f3bc96f1db16f649fc21dd18e5b0918f6
config ARCH_HAS_BROKEN_DWARF5
	def_bool y
	depends on RISCV_USE_LINKER_RELAXATION
	# https://github.com/llvm/llvm-project/commit/1df5ea29b43690b6622db2cad7b745607ca4de6a
	depends on AS_IS_LLVM && AS_VERSION < 180000
	# https://github.com/llvm/llvm-project/commit/7ffabb61a5569444b5ac9322e22e5471cc5e4a77
	depends on LD_IS_LLD && LLD_VERSION < 180000

config ARCH_MMAP_RND_BITS_MIN
	default 18 if 64BIT
	default 8

config ARCH_MMAP_RND_COMPAT_BITS_MIN
	default 8

# max bits determined by the following formula:
#  VA_BITS - PAGE_SHIFT - 3
config ARCH_MMAP_RND_BITS_MAX
	default 24 if 64BIT # SV39 based
	default 17

config ARCH_MMAP_RND_COMPAT_BITS_MAX
	default 17

# set if we run in machine mode, cleared if we run in supervisor mode
config RISCV_M_MODE
	bool "Build a kernel that runs in machine mode"
	depends on !MMU
	default y
	help
	  Select this option if you want to run the kernel in M-mode,
	  without the assistance of any other firmware.

# set if we are running in S-mode and can use SBI calls
config RISCV_SBI
	bool
	depends on !RISCV_M_MODE
	default y

config MMU
	bool "MMU-based Paged Memory Management Support"
	default y
	help
	  Select if you want MMU-based virtualised addressing space
	  support by paged memory management. If unsure, say 'Y'.

config PAGE_OFFSET
	hex
	default 0x80000000 if !MMU && RISCV_M_MODE
	default 0x80200000 if !MMU
	default 0xc0000000 if 32BIT
	default 0xff60000000000000 if 64BIT

config KASAN_SHADOW_OFFSET
	hex
	depends on KASAN_GENERIC
	default 0xdfffffff00000000 if 64BIT
	default 0xffffffff if 32BIT

config ARCH_FLATMEM_ENABLE
	def_bool !NUMA

config ARCH_SPARSEMEM_ENABLE
	def_bool y
	depends on MMU
	select SPARSEMEM_STATIC if 32BIT && SPARSEMEM
	select SPARSEMEM_VMEMMAP_ENABLE if 64BIT

config ARCH_SELECT_MEMORY_MODEL
	def_bool ARCH_SPARSEMEM_ENABLE

config ARCH_SUPPORTS_UPROBES
	def_bool y

config STACKTRACE_SUPPORT
	def_bool y

config GENERIC_BUG
	def_bool y
	depends on BUG
	select GENERIC_BUG_RELATIVE_POINTERS if 64BIT

config GENERIC_BUG_RELATIVE_POINTERS
	bool

config GENERIC_CALIBRATE_DELAY
	def_bool y

config GENERIC_CSUM
	def_bool y

config GENERIC_HWEIGHT
	def_bool y

config FIX_EARLYCON_MEM
	def_bool MMU

config ILLEGAL_POINTER_VALUE
	hex
	default 0 if 32BIT
	default 0xdead000000000000 if 64BIT

config PGTABLE_LEVELS
	int
	default 5 if 64BIT
	default 2

config LOCKDEP_SUPPORT
	def_bool y

config RISCV_DMA_NONCOHERENT
	bool
	select ARCH_HAS_DMA_PREP_COHERENT
	select ARCH_HAS_SETUP_DMA_OPS
	select ARCH_HAS_SYNC_DMA_FOR_CPU
	select ARCH_HAS_SYNC_DMA_FOR_DEVICE
	select DMA_BOUNCE_UNALIGNED_KMALLOC if SWIOTLB

config RISCV_NONSTANDARD_CACHE_OPS
	bool
	help
	  This enables function pointer support for non-standard noncoherent
	  systems to handle cache management.

config AS_HAS_INSN
	def_bool $(as-instr,.insn r 51$(comma) 0$(comma) 0$(comma) t0$(comma) t0$(comma) zero)

config AS_HAS_OPTION_ARCH
	# https://github.com/llvm/llvm-project/commit/9e8ed3403c191ab9c4903e8eeb8f732ff8a43cb4
	def_bool y
	depends on $(as-instr, .option arch$(comma) +m)

source "arch/riscv/Kconfig.socs"
source "arch/riscv/Kconfig.errata"

menu "Platform type"

config NONPORTABLE
	bool "Allow configurations that result in non-portable kernels"
	help
	  RISC-V kernel binaries are compatible between all known systems
	  whenever possible, but there are some use cases that can only be
	  satisfied by configurations that result in kernel binaries that are
	  not portable between systems.

	  Selecting N does not guarantee kernels will be portable to all known
	  systems.  Selecting any of the options guarded by NONPORTABLE will
	  result in kernel binaries that are unlikely to be portable between
	  systems.

	  If unsure, say N.

choice
	prompt "Base ISA"
	default ARCH_RV64I
	help
	  This selects the base ISA that this kernel will target and must match
	  the target platform.

config ARCH_RV32I
	bool "RV32I"
	depends on NONPORTABLE
	select 32BIT
	select GENERIC_LIB_ASHLDI3
	select GENERIC_LIB_ASHRDI3
	select GENERIC_LIB_LSHRDI3
	select GENERIC_LIB_UCMPDI2

config ARCH_RV64I
	bool "RV64I"
	select 64BIT
	select ARCH_SUPPORTS_INT128 if CC_HAS_INT128
	select SWIOTLB if MMU

endchoice

# We must be able to map all physical memory into the kernel, but the compiler
# is still a bit more efficient when generating code if it's setup in a manner
# such that it can only map 2GiB of memory.
choice
	prompt "Kernel Code Model"
	default CMODEL_MEDLOW if 32BIT
	default CMODEL_MEDANY if 64BIT

	config CMODEL_MEDLOW
		bool "medium low code model"
	config CMODEL_MEDANY
		bool "medium any code model"
endchoice

config MODULE_SECTIONS
	bool
	select HAVE_MOD_ARCH_SPECIFIC

config SMP
	bool "Symmetric Multi-Processing"
	help
	  This enables support for systems with more than one CPU.  If
	  you say N here, the kernel will run on single and
	  multiprocessor machines, but will use only one CPU of a
	  multiprocessor machine. If you say Y here, the kernel will run
	  on many, but not all, single processor machines. On a single
	  processor machine, the kernel will run faster if you say N
	  here.

	  If you don't know what to do here, say N.

config SCHED_MC
	bool "Multi-core scheduler support"
	depends on SMP
	help
	  Multi-core scheduler support improves the CPU scheduler's decision
	  making when dealing with multi-core CPU chips at a cost of slightly
	  increased overhead in some places. If unsure say N here.

config NR_CPUS
	int "Maximum number of CPUs (2-512)"
	depends on SMP
	range 2 512 if !RISCV_SBI_V01
	range 2 32 if RISCV_SBI_V01 && 32BIT
	range 2 64 if RISCV_SBI_V01 && 64BIT
	default "32" if 32BIT
	default "64" if 64BIT

config HOTPLUG_CPU
	bool "Support for hot-pluggable CPUs"
	depends on SMP
	select GENERIC_IRQ_MIGRATION
	help

	  Say Y here to experiment with turning CPUs off and on.  CPUs
	  can be controlled through /sys/devices/system/cpu.

	  Say N if you want to disable CPU hotplug.

choice
	prompt "CPU Tuning"
	default TUNE_GENERIC

config TUNE_GENERIC
	bool "generic"

endchoice

# Common NUMA Features
config NUMA
	bool "NUMA Memory Allocation and Scheduler Support"
	depends on SMP && MMU
	select ARCH_SUPPORTS_NUMA_BALANCING
	select GENERIC_ARCH_NUMA
	select HAVE_SETUP_PER_CPU_AREA
	select NEED_PER_CPU_EMBED_FIRST_CHUNK
	select NEED_PER_CPU_PAGE_FIRST_CHUNK
	select OF_NUMA
	select USE_PERCPU_NUMA_NODE_ID
	help
	  Enable NUMA (Non-Uniform Memory Access) support.

	  The kernel will try to allocate memory used by a CPU on the
	  local memory of the CPU and add some more NUMA awareness to the kernel.

config NODES_SHIFT
	int "Maximum NUMA Nodes (as a power of 2)"
	range 1 10
	default "2"
	depends on NUMA
	help
	  Specify the maximum number of NUMA Nodes available on the target
	  system.  Increases memory reserved to accommodate various tables.

config RISCV_ALTERNATIVE
	bool
	depends on !XIP_KERNEL
	help
	  This Kconfig allows the kernel to automatically patch the
	  erratum or cpufeature required by the execution platform at run
	  time. The code patching overhead is minimal, as it's only done
	  once at boot and once on each module load.

config RISCV_ALTERNATIVE_EARLY
	bool
	depends on RISCV_ALTERNATIVE
	help
	  Allows early patching of the kernel for special errata

config RISCV_ISA_C
	bool "Emit compressed instructions when building Linux"
	default y
	help
	  Adds "C" to the ISA subsets that the toolchain is allowed to emit
	  when building Linux, which results in compressed instructions in the
	  Linux binary.

	  If you don't know what to do here, say Y.

config RISCV_ISA_SVNAPOT
	bool "Svnapot extension support for supervisor mode NAPOT pages"
	depends on 64BIT && MMU
	depends on RISCV_ALTERNATIVE
	default y
	help
	  Allow kernel to detect the Svnapot ISA-extension dynamically at boot
	  time and enable its usage.

	  The Svnapot extension is used to mark contiguous PTEs as a range
	  of contiguous virtual-to-physical translations for a naturally
	  aligned power-of-2 (NAPOT) granularity larger than the base 4KB page
	  size. When HUGETLBFS is also selected this option unconditionally
	  allocates some memory for each NAPOT page size supported by the kernel.
	  When optimizing for low memory consumption and for platforms without
	  the Svnapot extension, it may be better to say N here.

	  If you don't know what to do here, say Y.

config RISCV_ISA_SVPBMT
	bool "Svpbmt extension support for supervisor mode page-based memory types"
	depends on 64BIT && MMU
	depends on RISCV_ALTERNATIVE
	default y
	help
	   Adds support to dynamically detect the presence of the Svpbmt
	   ISA-extension (Supervisor-mode: page-based memory types) and
	   enable its usage.

	   The memory type for a page contains a combination of attributes
	   that indicate the cacheability, idempotency, and ordering
	   properties for access to that page.

	   The Svpbmt extension is only available on 64-bit cpus.

	   If you don't know what to do here, say Y.

config TOOLCHAIN_HAS_V
	bool
	default y
	depends on !64BIT || $(cc-option,-mabi=lp64 -march=rv64imv)
	depends on !32BIT || $(cc-option,-mabi=ilp32 -march=rv32imv)
	depends on LLD_VERSION >= 140000 || LD_VERSION >= 23800
	depends on AS_HAS_OPTION_ARCH

config RISCV_ISA_V
	bool "VECTOR extension support"
	depends on TOOLCHAIN_HAS_V
	depends on FPU
	select DYNAMIC_SIGFRAME
	default y
	help
	  Say N here if you want to disable all vector related procedure
	  in the kernel.

	  If you don't know what to do here, say Y.

config RISCV_ISA_V_DEFAULT_ENABLE
	bool "Enable userspace Vector by default"
	depends on RISCV_ISA_V
	default y
	help
	  Say Y here if you want to enable Vector in userspace by default.
	  Otherwise, userspace has to make explicit prctl() call to enable
	  Vector, or enable it via the sysctl interface.

	  If you don't know what to do here, say Y.

config RISCV_ISA_V_UCOPY_THRESHOLD
	int "Threshold size for vectorized user copies"
	depends on RISCV_ISA_V
	default 768
	help
	  Prefer using vectorized copy_to_user()/copy_from_user() when the
	  workload size exceeds this value.

config RISCV_ISA_V_PREEMPTIVE
	bool "Run kernel-mode Vector with kernel preemption"
	depends on PREEMPTION
	depends on RISCV_ISA_V
	default y
	help
	  Usually, in-kernel SIMD routines are run with preemption disabled.
	  Functions which envoke long running SIMD thus must yield core's
	  vector unit to prevent blocking other tasks for too long.

	  This config allows kernel to run SIMD without explicitly disable
	  preemption. Enabling this config will result in higher memory
	  consumption due to the allocation of per-task's kernel Vector context.

config RISCV_ISA_ZAWRS
	bool "Zawrs extension support for more efficient busy waiting"
	depends on RISCV_ALTERNATIVE
	default y
	help
	  The Zawrs extension defines instructions to be used in polling loops
	  which allow a hart to enter a low-power state or to trap to the
	  hypervisor while waiting on a store to a memory location. Enable the
	  use of these instructions in the kernel when the Zawrs extension is
	  detected at boot.

	  If you don't know what to do here, say Y.

config TOOLCHAIN_HAS_ZBB
	bool
	default y
	depends on !64BIT || $(cc-option,-mabi=lp64 -march=rv64ima_zbb)
	depends on !32BIT || $(cc-option,-mabi=ilp32 -march=rv32ima_zbb)
	depends on LLD_VERSION >= 150000 || LD_VERSION >= 23900
	depends on AS_HAS_OPTION_ARCH

# This symbol indicates that the toolchain supports all v1.0 vector crypto
# extensions, including Zvk*, Zvbb, and Zvbc.  LLVM added all of these at once.
# binutils added all except Zvkb, then added Zvkb.  So we just check for Zvkb.
config TOOLCHAIN_HAS_VECTOR_CRYPTO
	def_bool $(as-instr, .option arch$(comma) +v$(comma) +zvkb)
	depends on AS_HAS_OPTION_ARCH

config RISCV_ISA_ZBA
	bool "Zba extension support for bit manipulation instructions"
	default y
	help
	   Add support for enabling optimisations in the kernel when the Zba
	   extension is detected at boot.

	   The Zba extension provides instructions to accelerate the generation
	   of addresses that index into arrays of basic data types.

	   If you don't know what to do here, say Y.

config RISCV_ISA_ZBB
	bool "Zbb extension support for bit manipulation instructions"
	depends on TOOLCHAIN_HAS_ZBB
	depends on RISCV_ALTERNATIVE
	default y
	help
	   Adds support to dynamically detect the presence of the ZBB
	   extension (basic bit manipulation) and enable its usage.

	   The Zbb extension provides instructions to accelerate a number
	   of bit-specific operations (count bit population, sign extending,
	   bitrotation, etc).

	   If you don't know what to do here, say Y.

config TOOLCHAIN_HAS_ZBC
	bool
	default y
	depends on !64BIT || $(cc-option,-mabi=lp64 -march=rv64ima_zbc)
	depends on !32BIT || $(cc-option,-mabi=ilp32 -march=rv32ima_zbc)
	depends on LLD_VERSION >= 150000 || LD_VERSION >= 23900
	depends on AS_HAS_OPTION_ARCH

config RISCV_ISA_ZBC
	bool "Zbc extension support for carry-less multiplication instructions"
	depends on TOOLCHAIN_HAS_ZBC
	depends on MMU
	depends on RISCV_ALTERNATIVE
	default y
	help
	   Adds support to dynamically detect the presence of the Zbc
	   extension (carry-less multiplication) and enable its usage.

	   The Zbc extension could accelerate CRC (cyclic redundancy check)
	   calculations.

	   If you don't know what to do here, say Y.

config RISCV_ISA_ZICBOM
	bool "Zicbom extension support for non-coherent DMA operation"
	depends on MMU
	depends on RISCV_ALTERNATIVE
	default y
	select RISCV_DMA_NONCOHERENT
	select DMA_DIRECT_REMAP
	help
	   Adds support to dynamically detect the presence of the ZICBOM
	   extension (Cache Block Management Operations) and enable its
	   usage.

	   The Zicbom extension can be used to handle for example
	   non-coherent DMA support on devices that need it.

	   If you don't know what to do here, say Y.

config RISCV_ISA_ZICBOZ
	bool "Zicboz extension support for faster zeroing of memory"
	depends on RISCV_ALTERNATIVE
	default y
	help
	   Enable the use of the Zicboz extension (cbo.zero instruction)
	   when available.

	   The Zicboz extension is used for faster zeroing of memory.

	   If you don't know what to do here, say Y.

config TOOLCHAIN_NEEDS_EXPLICIT_ZICSR_ZIFENCEI
	def_bool y
	# https://sourceware.org/git/?p=binutils-gdb.git;a=commit;h=aed44286efa8ae8717a77d94b51ac3614e2ca6dc
	# https://gcc.gnu.org/git/?p=gcc.git;a=commit;h=98416dbb0a62579d4a7a4a76bab51b5b52fec2cd
	depends on AS_IS_GNU && AS_VERSION >= 23600
	help
	  Binutils-2.38 and GCC-12.1.0 bumped the default ISA spec to the newer
	  20191213 version, which moves some instructions from the I extension to
	  the Zicsr and Zifencei extensions. This requires explicitly specifying
	  Zicsr and Zifencei when binutils >= 2.38 or GCC >= 12.1.0. Zicsr
	  and Zifencei are supported in binutils from version 2.36 onwards.
	  To make life easier, and avoid forcing toolchains that default to a
	  newer ISA spec to version 2.2, relax the check to binutils >= 2.36.
	  For clang < 17 or GCC < 11.3.0, for which this is not possible or need
	  special treatment, this is dealt with in TOOLCHAIN_NEEDS_OLD_ISA_SPEC.

config TOOLCHAIN_NEEDS_OLD_ISA_SPEC
	def_bool y
	depends on TOOLCHAIN_NEEDS_EXPLICIT_ZICSR_ZIFENCEI
	# https://github.com/llvm/llvm-project/commit/22e199e6afb1263c943c0c0d4498694e15bf8a16
	# https://gcc.gnu.org/git/?p=gcc.git;a=commit;h=d29f5d6ab513c52fd872f532c492e35ae9fd6671
	depends on (CC_IS_CLANG && CLANG_VERSION < 170000) || (CC_IS_GCC && GCC_VERSION < 110300)
	help
	  Certain versions of clang and GCC do not support zicsr and zifencei via
	  -march. This option causes an older ISA spec compatible with these older
	  versions of clang and GCC to be passed to GAS, which has the same result
	  as passing zicsr and zifencei to -march.

config FPU
	bool "FPU support"
	default y
	help
	  Say N here if you want to disable all floating-point related procedure
	  in the kernel.

	  If you don't know what to do here, say Y.

config IRQ_STACKS
	bool "Independent irq & softirq stacks" if EXPERT
	default y
	select HAVE_IRQ_EXIT_ON_IRQ_STACK
	select HAVE_SOFTIRQ_ON_OWN_STACK
	help
	  Add independent irq & softirq stacks for percpu to prevent kernel stack
	  overflows. We may save some memory footprint by disabling IRQ_STACKS.

config THREAD_SIZE_ORDER
	int "Kernel stack size (in power-of-two numbers of page size)" if VMAP_STACK && EXPERT
	range 0 4
	default 1 if 32BIT
	default 2
	help
	  Specify the Pages of thread stack size (from 4KB to 64KB), which also
	  affects irq stack size, which is equal to thread stack size.

config RISCV_MISALIGNED
	bool
	select SYSCTL_ARCH_UNALIGN_ALLOW
	help
	  Embed support for emulating misaligned loads and stores.

choice
	prompt "Unaligned Accesses Support"
	default RISCV_PROBE_UNALIGNED_ACCESS
	help
	  This determines the level of support for unaligned accesses. This
	  information is used by the kernel to perform optimizations. It is also
	  exposed to user space via the hwprobe syscall. The hardware will be
	  probed at boot by default.

config RISCV_PROBE_UNALIGNED_ACCESS
	bool "Probe for hardware unaligned access support"
	select RISCV_MISALIGNED
	help
	  During boot, the kernel will run a series of tests to determine the
	  speed of unaligned accesses. This probing will dynamically determine
	  the speed of unaligned accesses on the underlying system. If unaligned
	  memory accesses trap into the kernel as they are not supported by the
	  system, the kernel will emulate the unaligned accesses to preserve the
	  UABI.

config RISCV_EMULATED_UNALIGNED_ACCESS
	bool "Emulate unaligned access where system support is missing"
	select RISCV_MISALIGNED
	help
	  If unaligned memory accesses trap into the kernel as they are not
	  supported by the system, the kernel will emulate the unaligned
	  accesses to preserve the UABI. When the underlying system does support
	  unaligned accesses, the unaligned accesses are assumed to be slow.

config RISCV_SLOW_UNALIGNED_ACCESS
	bool "Assume the system supports slow unaligned memory accesses"
	depends on NONPORTABLE
	help
	  Assume that the system supports slow unaligned memory accesses. The
	  kernel and userspace programs may not be able to run at all on systems
	  that do not support unaligned memory accesses.

config RISCV_EFFICIENT_UNALIGNED_ACCESS
	bool "Assume the system supports fast unaligned memory accesses"
	depends on NONPORTABLE
	select DCACHE_WORD_ACCESS if MMU
	select HAVE_EFFICIENT_UNALIGNED_ACCESS
	help
	  Assume that the system supports fast unaligned memory accesses. When
	  enabled, this option improves the performance of the kernel on such
	  systems. However, the kernel and userspace programs will run much more
	  slowly, or will not be able to run at all, on systems that do not
	  support efficient unaligned memory accesses.

endchoice

source "arch/riscv/Kconfig.vendor"

endmenu # "Platform type"

menu "Kernel features"

source "kernel/Kconfig.hz"

config RISCV_SBI_V01
	bool "SBI v0.1 support"
	depends on RISCV_SBI
	help
	  This config allows kernel to use SBI v0.1 APIs. This will be
	  deprecated in future once legacy M-mode software are no longer in use.

config RISCV_BOOT_SPINWAIT
	bool "Spinwait booting method"
	depends on SMP
	default y if RISCV_SBI_V01 || RISCV_M_MODE
	help
	  This enables support for booting Linux via spinwait method. In the
	  spinwait method, all cores randomly jump to Linux. One of the cores
	  gets chosen via lottery and all other keep spinning on a percpu
	  variable. This method cannot support CPU hotplug and sparse hartid
	  scheme. It should be only enabled for M-mode Linux or platforms relying
	  on older firmware without SBI HSM extension. All other platforms should
	  rely on ordered booting via SBI HSM extension which gets chosen
	  dynamically at runtime if the firmware supports it.

	  Since spinwait is incompatible with sparse hart IDs, it requires
	  NR_CPUS be large enough to contain the physical hart ID of the first
	  hart to enter Linux.

	  If unsure what to do here, say N.

config ARCH_SUPPORTS_KEXEC
	def_bool y

config ARCH_SELECTS_KEXEC
	def_bool y
	depends on KEXEC
	select HOTPLUG_CPU if SMP

config ARCH_SUPPORTS_KEXEC_FILE
	def_bool 64BIT

config ARCH_SELECTS_KEXEC_FILE
	def_bool y
	depends on KEXEC_FILE
	select HAVE_IMA_KEXEC if IMA
	select KEXEC_ELF

config ARCH_SUPPORTS_KEXEC_PURGATORY
	def_bool ARCH_SUPPORTS_KEXEC_FILE

config ARCH_SUPPORTS_CRASH_DUMP
	def_bool y

config ARCH_DEFAULT_CRASH_DUMP
	def_bool y

config ARCH_HAS_GENERIC_CRASHKERNEL_RESERVATION
	def_bool CRASH_RESERVE

config COMPAT
	bool "Kernel support for 32-bit U-mode"
	default 64BIT
	depends on 64BIT && MMU
	help
	  This option enables support for a 32-bit U-mode running under a 64-bit
	  kernel at S-mode. riscv32-specific components such as system calls,
	  the user helper functions (vdso), signal rt_frame functions and the
	  ptrace interface are handled appropriately by the kernel.

	  If you want to execute 32-bit userspace applications, say Y.

config PARAVIRT
	bool "Enable paravirtualization code"
	depends on RISCV_SBI
	help
	  This changes the kernel so it can modify itself when it is run
	  under a hypervisor, potentially improving performance significantly
	  over full virtualization.

config PARAVIRT_TIME_ACCOUNTING
	bool "Paravirtual steal time accounting"
	depends on PARAVIRT
	help
	  Select this option to enable fine granularity task steal time
	  accounting. Time spent executing other tasks in parallel with
	  the current vCPU is discounted from the vCPU power. To account for
	  that, there can be a small performance impact.

	  If in doubt, say N here.

config RELOCATABLE
	bool "Build a relocatable kernel"
	depends on MMU && 64BIT && !XIP_KERNEL
	select MODULE_SECTIONS if MODULES
	help
          This builds a kernel as a Position Independent Executable (PIE),
          which retains all relocation metadata required to relocate the
          kernel binary at runtime to a different virtual address than the
          address it was linked at.
          Since RISCV uses the RELA relocation format, this requires a
          relocation pass at runtime even if the kernel is loaded at the
          same address it was linked at.

          If unsure, say N.

config RANDOMIZE_BASE
        bool "Randomize the address of the kernel image"
        select RELOCATABLE
        depends on MMU && 64BIT && !XIP_KERNEL
        help
          Randomizes the virtual address at which the kernel image is
          loaded, as a security feature that deters exploit attempts
          relying on knowledge of the location of kernel internals.

          It is the bootloader's job to provide entropy, by passing a
          random u64 value in /chosen/kaslr-seed at kernel entry.

          When booting via the UEFI stub, it will invoke the firmware's
          EFI_RNG_PROTOCOL implementation (if available) to supply entropy
          to the kernel proper. In addition, it will randomise the physical
          location of the kernel Image as well.

          If unsure, say N.

endmenu # "Kernel features"

menu "Boot options"

config CMDLINE
	string "Built-in kernel command line"
	help
	  For most platforms, the arguments for the kernel's command line
	  are provided at run-time, during boot. However, there are cases
	  where either no arguments are being provided or the provided
	  arguments are insufficient or even invalid.

	  When that occurs, it is possible to define a built-in command
	  line here and choose how the kernel should use it later on.

choice
	prompt "Built-in command line usage"
	depends on CMDLINE != ""
	default CMDLINE_FALLBACK
	help
	  Choose how the kernel will handle the provided built-in command
	  line.

config CMDLINE_FALLBACK
	bool "Use bootloader kernel arguments if available"
	help
	  Use the built-in command line as fallback in case we get nothing
	  during boot. This is the default behaviour.

config CMDLINE_EXTEND
	bool "Extend bootloader kernel arguments"
	help
	  The command-line arguments provided during boot will be
	  appended to the built-in command line. This is useful in
	  cases where the provided arguments are insufficient and
	  you don't want to or cannot modify them.

config CMDLINE_FORCE
	bool "Always use the default kernel command string"
	help
	  Always use the built-in command line, even if we get one during
	  boot. This is useful in case you need to override the provided
	  command line on systems where you don't have or want control
	  over it.

endchoice

config EFI_STUB
	bool

config EFI
	bool "UEFI runtime support"
	depends on OF && !XIP_KERNEL
	depends on MMU
	default y
	select ARCH_SUPPORTS_ACPI if 64BIT
	select EFI_GENERIC_STUB
	select EFI_PARAMS_FROM_FDT
	select EFI_RUNTIME_WRAPPERS
	select EFI_STUB
	select LIBFDT
	select RISCV_ISA_C
	select UCS2_STRING
	help
	  This option provides support for runtime services provided
	  by UEFI firmware (such as non-volatile variables, realtime
	  clock, and platform reset). A UEFI stub is also provided to
	  allow the kernel to be booted as an EFI application. This
	  is only useful on systems that have UEFI firmware.

config DMI
	bool "Enable support for SMBIOS (DMI) tables"
	depends on EFI
	default y
	help
	  This enables SMBIOS/DMI feature for systems.

	  This option is only useful on systems that have UEFI firmware.
	  However, even with this option, the resultant kernel should
	  continue to boot on existing non-UEFI platforms.

config CC_HAVE_STACKPROTECTOR_TLS
	def_bool $(cc-option,-mstack-protector-guard=tls -mstack-protector-guard-reg=tp -mstack-protector-guard-offset=0)

config STACKPROTECTOR_PER_TASK
	def_bool y
	depends on !RANDSTRUCT
	depends on STACKPROTECTOR && CC_HAVE_STACKPROTECTOR_TLS

config PHYS_RAM_BASE_FIXED
	bool "Explicitly specified physical RAM address"
	depends on NONPORTABLE
	default n

config PHYS_RAM_BASE
	hex "Platform Physical RAM address"
	depends on PHYS_RAM_BASE_FIXED
	default "0x80000000"
	help
	  This is the physical address of RAM in the system. It has to be
	  explicitly specified to run early relocations of read-write data
	  from flash to RAM.

config XIP_KERNEL
	bool "Kernel Execute-In-Place from ROM"
	depends on MMU && SPARSEMEM && NONPORTABLE
	# This prevents XIP from being enabled by all{yes,mod}config, which
	# fail to build since XIP doesn't support large kernels.
	depends on !COMPILE_TEST
	select PHYS_RAM_BASE_FIXED
	help
	  Execute-In-Place allows the kernel to run from non-volatile storage
	  directly addressable by the CPU, such as NOR flash. This saves RAM
	  space since the text section of the kernel is not loaded from flash
	  to RAM.  Read-write sections, such as the data section and stack,
	  are still copied to RAM.  The XIP kernel is not compressed since
	  it has to run directly from flash, so it will take more space to
	  store it.  The flash address used to link the kernel object files,
	  and for storing it, is configuration dependent. Therefore, if you
	  say Y here, you must know the proper physical address where to
	  store the kernel image depending on your own flash memory usage.

	  Also note that the make target becomes "make xipImage" rather than
	  "make zImage" or "make Image".  The final kernel binary to put in
	  ROM memory will be arch/riscv/boot/xipImage.

	  SPARSEMEM is required because the kernel text and rodata that are
	  flash resident are not backed by memmap, then any attempt to get
	  a struct page on those regions will trigger a fault.

	  If unsure, say N.

config XIP_PHYS_ADDR
	hex "XIP Kernel Physical Location"
	depends on XIP_KERNEL
	default "0x21000000"
	help
	  This is the physical address in your flash memory the kernel will
	  be linked for and stored to.  This address is dependent on your
	  own flash usage.

config RISCV_ISA_FALLBACK
	bool "Permit falling back to parsing riscv,isa for extension support by default"
	default y
	help
	  Parsing the "riscv,isa" devicetree property has been deprecated and
	  replaced by a list of explicitly defined strings. For compatibility
	  with existing platforms, the kernel will fall back to parsing the
	  "riscv,isa" property if the replacements are not found.

	  Selecting N here will result in a kernel that does not use the
	  fallback, unless the commandline "riscv_isa_fallback" parameter is
	  present.

	  Please see the dt-binding, located at
	  Documentation/devicetree/bindings/riscv/extensions.yaml for details
	  on the replacement properties, "riscv,isa-base" and
	  "riscv,isa-extensions".

config BUILTIN_DTB
	bool "Built-in device tree"
	depends on OF && NONPORTABLE
	help
	  Build a device tree into the Linux image.
	  This option should be selected if no bootloader is being used.
	  If unsure, say N.


config BUILTIN_DTB_SOURCE
	string "Built-in device tree source"
	depends on BUILTIN_DTB
	help
	  DTS file path (without suffix, relative to arch/riscv/boot/dts)
	  for the DTS file that will be used to produce the DTB linked into the
	  kernel.

endmenu # "Boot options"

config PORTABLE
	bool
	default !NONPORTABLE
	select EFI
	select MMU
	select OF

config ARCH_PROC_KCORE_TEXT
	def_bool y

menu "Power management options"

source "kernel/power/Kconfig"

config ARCH_HIBERNATION_POSSIBLE
	def_bool y

config ARCH_HIBERNATION_HEADER
	def_bool HIBERNATION

config ARCH_SUSPEND_POSSIBLE
	def_bool y

endmenu # "Power management options"

menu "CPU Power Management"

source "drivers/cpuidle/Kconfig"

source "drivers/cpufreq/Kconfig"

endmenu # "CPU Power Management"

source "arch/riscv/kvm/Kconfig"

source "drivers/acpi/Kconfig"
