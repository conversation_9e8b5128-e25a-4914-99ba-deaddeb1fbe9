# SPDX-License-Identifier: GPL-2.0
menu "DMA support"


config SH_DMA
	bool "SuperH on-chip DMA controller (DMAC) support"
	depends on CPU_SH3 || CPU_SH4
	default n

config SH_DMA_IRQ_MULTI
	bool
	depends on SH_DMA
	default y if CPU_SUBTYPE_SH7750  || CPU_SUBTYPE_SH7751  || \
		     CPU_SUBTYPE_SH7750S || CPU_SUBTYPE_SH7750R || \
		     CPU_SUBTYPE_SH7751R || CPU_SUBTYPE_SH7091  || \
		     CPU_SUBTYPE_SH7763  || CPU_SUBTYPE_SH7780  || \
		     CPU_SUBTYPE_SH7785  || CPU_SUBTYPE_SH7760

config SH_DMA_API
	depends on SH_DMA
	bool "SuperH DMA API support"
	default n
	help
	  SH_DMA_API always enabled DMA API of used SuperH.
	  If you want to use DMA ENGINE, you must not enable this.
	  Please enable DMA_ENGINE and SH_DMAE.

config NR_ONCHIP_DMA_CHANNELS
	int
	depends on SH_DMA
	default "4" if CPU_SUBTYPE_SH7709 || CPU_SUBTYPE_SH7750  || \
		       CPU_SUBTYPE_SH7750S || CPU_SUBTYPE_SH7751 || \
		       CPU_SUBTYPE_SH7091
	default "8" if CPU_SUBTYPE_SH7750R || CPU_SUBTYPE_SH7751R || \
		       CPU_SUBTYPE_SH7760
	default "12" if CPU_SUBTYPE_SH7723 || CPU_SUBTYPE_SH7724  || \
			CPU_SUBTYPE_SH7780 || CPU_SUBTYPE_SH7785
	default "6"
	help
	  This allows you to specify the number of channels that the on-chip
	  DMAC supports. This will be 4 for SH7709/SH7750/SH7750S/SH7751/SH7091,
	  8 for SH7750R/SH7751R/SH7760, and 12 for SH7723/SH7724/SH7780/SH7785.
	  Default is 6.

config SH_DMABRG
	bool "SH7760 DMABRG support"
	depends on CPU_SUBTYPE_SH7760
	help
	  The DMABRG does data transfers from main memory to Audio/USB units
	  of the SH7760.
	  Say Y if you want to use Audio/USB DMA on your SH7760 board.

config PVR2_DMA
	tristate "PowerVR 2 DMAC support"
	depends on SH_DREAMCAST && SH_DMA
	help
	  Selecting this will enable support for the PVR2 DMA controller.
	  As this chains off of the on-chip DMAC, that must also be
	  enabled by default.

	  This is primarily used by the pvr2fb framebuffer driver for
	  certain optimizations, but is not necessary for functionality.

	  If in doubt, say N.

config G2_DMA
	tristate "G2 Bus DMA support"
	depends on SH_DREAMCAST && SH_DMA_API
	help
	  This enables support for the DMA controller for the Dreamcast's
	  G2 bus. Drivers that want this will generally enable this on
	  their own.

	  If in doubt, say N.

endmenu
