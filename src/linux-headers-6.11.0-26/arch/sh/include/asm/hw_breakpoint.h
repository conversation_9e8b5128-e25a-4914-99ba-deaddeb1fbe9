/* SPDX-License-Identifier: GPL-2.0 */
#ifndef __ASM_SH_HW_BREAKPOINT_H
#define __ASM_SH_HW_BREAKPOINT_H

#include <uapi/asm/hw_breakpoint.h>

#define __ARCH_HW_BREAKPOINT_H

#include <linux/kdebug.h>
#include <linux/types.h>

struct arch_hw_breakpoint {
	unsigned long	address;
	u16		len;
	u16		type;
};

enum {
	SH_BREAKPOINT_READ	= (1 << 1),
	SH_BREAKPOINT_WRITE	= (1 << 2),
	SH_BREAKPOINT_RW	= SH_BREAKPOINT_READ | SH_BREAKPOINT_WRITE,

	SH_BREAKPOINT_LEN_1	= (1 << 12),
	SH_BREAKPOINT_LEN_2	= (1 << 13),
	SH_BREAKPOINT_LEN_4	= SH_BREAKPOINT_LEN_1 | SH_BREAKPOINT_LEN_2,
	SH_BREAKPOINT_LEN_8	= (1 << 14),
};

struct sh_ubc {
	const char	*name;
	unsigned int	num_events;
	unsigned int	trap_nr;
	void		(*enable)(struct arch_hw_breakpoint *, int);
	void		(*disable)(struct arch_hw_breakpoint *, int);
	void		(*enable_all)(unsigned long);
	void		(*disable_all)(void);
	unsigned long	(*active_mask)(void);
	unsigned long	(*triggered_mask)(void);
	void		(*clear_triggered_mask)(unsigned long);
	struct clk	*clk;	/* optional interface clock / MSTP bit */
};

struct perf_event_attr;
struct perf_event;
struct task_struct;
struct pmu;

/* Maximum number of UBC channels */
#define HBP_NUM		2

#define hw_breakpoint_slots(type) (HBP_NUM)

/* arch/sh/kernel/hw_breakpoint.c */
extern int arch_check_bp_in_kernelspace(struct arch_hw_breakpoint *hw);
extern int arch_bp_generic_fields(int sh_len, int sh_type, int *gen_len,
				  int *gen_type);
extern int hw_breakpoint_arch_parse(struct perf_event *bp,
				    const struct perf_event_attr *attr,
				    struct arch_hw_breakpoint *hw);
extern int hw_breakpoint_exceptions_notify(struct notifier_block *unused,
					   unsigned long val, void *data);

int arch_install_hw_breakpoint(struct perf_event *bp);
void arch_uninstall_hw_breakpoint(struct perf_event *bp);
void hw_breakpoint_pmu_read(struct perf_event *bp);

extern void arch_fill_perf_breakpoint(struct perf_event *bp);
extern int register_sh_ubc(struct sh_ubc *);

extern struct pmu perf_ops_bp;

#endif /* __ASM_SH_HW_BREAKPOINT_H */
