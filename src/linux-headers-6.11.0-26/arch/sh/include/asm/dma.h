/* SPDX-License-Identifier: GPL-2.0
 *
 * include/asm-sh/dma.h
 *
 * Copyright (C) 2003, 2004  <PERSON>
 */
#ifndef __ASM_SH_DMA_H
#define __ASM_SH_DMA_H

#include <linux/spinlock.h>
#include <linux/wait.h>
#include <linux/sched.h>
#include <linux/device.h>
#include <asm-generic/dma.h>

/*
 * Read and write modes can mean drastically different things depending on the
 * channel configuration. Consult your DMAC documentation and module
 * implementation for further clues.
 */
#define DMA_MODE_READ		0x00
#define DMA_MODE_WRITE		0x01
#define DMA_MODE_MASK		0x01

#define DMA_AUTOINIT		0x10

/*
 * DMAC (dma_info) flags
 */
enum {
	DMAC_CHANNELS_CONFIGURED	= 0x01,
	DMAC_CHANNELS_TEI_CAPABLE	= 0x02,	/* Transfer end interrupt */
};

/*
 * DMA channel capabilities / flags
 */
enum {
	DMA_CONFIGURED			= 0x01,

	/*
	 * Transfer end interrupt, inherited from DMAC.
	 * wait_queue used in dma_wait_for_completion.
	 */
	DMA_TEI_CAPABLE			= 0x02,
};

extern spinlock_t dma_spin_lock;

struct dma_channel;

struct dma_ops {
	int (*request)(struct dma_channel *chan);
	void (*free)(struct dma_channel *chan);

	int (*get_residue)(struct dma_channel *chan);
	int (*xfer)(struct dma_channel *chan);
	int (*configure)(struct dma_channel *chan, unsigned long flags);
};

struct dma_channel {
	char dev_id[16];		/* unique name per DMAC of channel */

	unsigned int chan;		/* DMAC channel number */
	unsigned int vchan;		/* Virtual channel number */

	unsigned int mode;
	unsigned int count;

	unsigned long sar;
	unsigned long dar;

	const char **caps;

	unsigned long flags;
	atomic_t busy;

	wait_queue_head_t wait_queue;

	struct device dev;
	void *priv_data;
};

struct dma_info {
	struct platform_device *pdev;

	const char *name;
	unsigned int nr_channels;
	unsigned long flags;

	struct dma_ops *ops;
	struct dma_channel *channels;

	struct list_head list;
	int first_channel_nr;
	int first_vchannel_nr;
};

struct dma_chan_caps {
	int ch_num;
	const char **caplist;
};

#define to_dma_channel(channel) container_of(channel, struct dma_channel, dev)

/* arch/sh/drivers/dma/dma-api.c */
extern int dma_xfer(unsigned int chan, unsigned long from,
		    unsigned long to, size_t size, unsigned int mode);

#define dma_write(chan, from, to, size)	\
	dma_xfer(chan, from, to, size, DMA_MODE_WRITE)
#define dma_write_page(chan, from, to)	\
	dma_write(chan, from, to, PAGE_SIZE)

#define dma_read(chan, from, to, size)	\
	dma_xfer(chan, from, to, size, DMA_MODE_READ)
#define dma_read_page(chan, from, to)	\
	dma_read(chan, from, to, PAGE_SIZE)

extern int get_dma_residue(unsigned int chan);
extern struct dma_info *get_dma_info(unsigned int chan);
extern struct dma_channel *get_dma_channel(unsigned int chan);
extern void dma_wait_for_completion(unsigned int chan);
extern void dma_configure_channel(unsigned int chan, unsigned long flags);

extern int register_dmac(struct dma_info *info);
extern void unregister_dmac(struct dma_info *info);

/* arch/sh/drivers/dma/dma-sysfs.c */
extern int dma_create_sysfs_files(struct dma_channel *, struct dma_info *);
extern void dma_remove_sysfs_files(struct dma_channel *, struct dma_info *);

#endif /* __ASM_SH_DMA_H */
