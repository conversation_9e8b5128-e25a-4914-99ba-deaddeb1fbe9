/* SPDX-License-Identifier: GPL-2.0
 *
 * SH7786 Pinmux
 *
 * Copyright (C) 2008, 2009  Renesas Solutions Corp.
 * <PERSON>ninori Morimoto <<EMAIL>>
 *
 *  Based on sh7785.h
 */

#ifndef __CPU_SH7786_H__
#define __CPU_SH7786_H__

#include <linux/io.h>

enum {
	/* PA */
	GPIO_PA7, GPIO_PA6, GPIO_PA5, GPIO_PA4,
	GPIO_PA3, GPIO_PA2, GPIO_PA1, GPIO_PA0,

	/* PB */
	GPIO_PB7, GPIO_PB6, GPIO_PB5, GPIO_PB4,
	GPIO_PB3, GPIO_PB2, GPIO_PB1, GPIO_PB0,

	/* PC */
	GPIO_PC7, GPIO_PC6, GPIO_PC5, GPIO_PC4,
	GPIO_PC3, GPIO_PC2, GPIO_PC1, GPIO_PC0,

	/* PD */
	GPIO_PD7, GPIO_PD6, GP<PERSON>_PD5, <PERSON><PERSON>_PD4,
	<PERSON><PERSON>_PD3, <PERSON>IO_PD2, <PERSON><PERSON>_PD1, <PERSON><PERSON>_PD0,

	/* PE */
	GPIO_PE7, GPIO_PE6,

	/* PF */
	GPIO_PF7, GPIO_PF6, GPIO_PF5, GPIO_PF4,
	GPIO_PF3, GPIO_PF2, GPIO_PF1, GPIO_PF0,

	/* PG */
	GPIO_PG7, GPIO_PG6, GPIO_PG5,

	/* PH */
	GPIO_PH7, GPIO_PH6, GPIO_PH5, GPIO_PH4,
	GPIO_PH3, GPIO_PH2, GPIO_PH1, GPIO_PH0,

	/* PJ */
	GPIO_PJ7, GPIO_PJ6, GPIO_PJ5, GPIO_PJ4,
	GPIO_PJ3, GPIO_PJ2, GPIO_PJ1,

	/* DU */
	GPIO_FN_DCLKIN, GPIO_FN_DCLKOUT, GPIO_FN_ODDF,
	GPIO_FN_VSYNC, GPIO_FN_HSYNC, GPIO_FN_CDE, GPIO_FN_DISP,
	GPIO_FN_DR0, GPIO_FN_DG0, GPIO_FN_DB0,
	GPIO_FN_DR1, GPIO_FN_DG1, GPIO_FN_DB1,
	GPIO_FN_DR2, GPIO_FN_DG2, GPIO_FN_DB2,
	GPIO_FN_DR3, GPIO_FN_DG3, GPIO_FN_DB3,
	GPIO_FN_DR4, GPIO_FN_DG4, GPIO_FN_DB4,
	GPIO_FN_DR5, GPIO_FN_DG5, GPIO_FN_DB5,

	/* Eth */
	GPIO_FN_ETH_MAGIC, GPIO_FN_ETH_LINK, GPIO_FN_ETH_TX_ER,
	GPIO_FN_ETH_TX_EN, GPIO_FN_ETH_MDIO, GPIO_FN_ETH_RX_CLK,
	GPIO_FN_ETH_MDC, GPIO_FN_ETH_COL, GPIO_FN_ETH_TX_CLK,
	GPIO_FN_ETH_CRS, GPIO_FN_ETH_RX_DV, GPIO_FN_ETH_RX_ER,
	GPIO_FN_ETH_TXD3, GPIO_FN_ETH_TXD2, GPIO_FN_ETH_TXD1, GPIO_FN_ETH_TXD0,
	GPIO_FN_ETH_RXD3, GPIO_FN_ETH_RXD2, GPIO_FN_ETH_RXD1, GPIO_FN_ETH_RXD0,

	/* HSPI */
	GPIO_FN_HSPI_CLK, GPIO_FN_HSPI_CS, GPIO_FN_HSPI_RX, GPIO_FN_HSPI_TX,

	/* SCIF0 */
	GPIO_FN_SCIF0_CTS, GPIO_FN_SCIF0_RTS, GPIO_FN_SCIF0_SCK,
	GPIO_FN_SCIF0_RXD, GPIO_FN_SCIF0_TXD,

	/* SCIF1 */
	GPIO_FN_SCIF1_SCK, GPIO_FN_SCIF1_RXD, GPIO_FN_SCIF1_TXD,

	/* SCIF3 */
	GPIO_FN_SCIF3_SCK, GPIO_FN_SCIF3_RXD, GPIO_FN_SCIF3_TXD,

	/* SCIF4 */
	GPIO_FN_SCIF4_SCK, GPIO_FN_SCIF4_RXD, GPIO_FN_SCIF4_TXD,

	/* SCIF5 */
	GPIO_FN_SCIF5_SCK, GPIO_FN_SCIF5_RXD, GPIO_FN_SCIF5_TXD,

	/* LBSC */
	GPIO_FN_BREQ, GPIO_FN_IOIS16, GPIO_FN_CE2B, GPIO_FN_CE2A, GPIO_FN_BACK,

	/* FLCTL */
	GPIO_FN_FALE, GPIO_FN_FRB, GPIO_FN_FSTATUS,
	GPIO_FN_FSE, GPIO_FN_FCLE,

	/* DMAC */
	GPIO_FN_DACK0, GPIO_FN_DREQ0, GPIO_FN_DRAK0,
	GPIO_FN_DACK1, GPIO_FN_DREQ1, GPIO_FN_DRAK1,
	GPIO_FN_DACK2, GPIO_FN_DREQ2, GPIO_FN_DRAK2,
	GPIO_FN_DACK3, GPIO_FN_DREQ3, GPIO_FN_DRAK3,

	/* USB */
	GPIO_FN_USB_OVC0, GPIO_FN_USB_PENC0,
	GPIO_FN_USB_OVC1, GPIO_FN_USB_PENC1,

	/* HAC */
	GPIO_FN_HAC_RES,
	GPIO_FN_HAC0_SDOUT, GPIO_FN_HAC0_SDIN,
	GPIO_FN_HAC0_SYNC, GPIO_FN_HAC0_BITCLK,
	GPIO_FN_HAC1_SDOUT, GPIO_FN_HAC1_SDIN,
	GPIO_FN_HAC1_SYNC, GPIO_FN_HAC1_BITCLK,

	/* SSI */
	GPIO_FN_SSI0_SDATA, GPIO_FN_SSI0_SCK, GPIO_FN_SSI0_WS, GPIO_FN_SSI0_CLK,
	GPIO_FN_SSI1_SDATA, GPIO_FN_SSI1_SCK, GPIO_FN_SSI1_WS, GPIO_FN_SSI1_CLK,
	GPIO_FN_SSI2_SDATA, GPIO_FN_SSI2_SCK, GPIO_FN_SSI2_WS,
	GPIO_FN_SSI3_SDATA, GPIO_FN_SSI3_SCK, GPIO_FN_SSI3_WS,

	/* SDIF1 */
	GPIO_FN_SDIF1CMD, GPIO_FN_SDIF1CD, GPIO_FN_SDIF1WP, GPIO_FN_SDIF1CLK,
	GPIO_FN_SDIF1D3, GPIO_FN_SDIF1D2, GPIO_FN_SDIF1D1, GPIO_FN_SDIF1D0,

	/* SDIF0 */
	GPIO_FN_SDIF0CMD, GPIO_FN_SDIF0CD, GPIO_FN_SDIF0WP, GPIO_FN_SDIF0CLK,
	GPIO_FN_SDIF0D3, GPIO_FN_SDIF0D2, GPIO_FN_SDIF0D1, GPIO_FN_SDIF0D0,

	/* TMU */
	GPIO_FN_TCLK,

	/* INTC */
	GPIO_FN_IRL7, GPIO_FN_IRL6, GPIO_FN_IRL5, GPIO_FN_IRL4,
};

static inline u32 sh7786_mm_sel(void)
{
	return __raw_readl((const volatile void __iomem *)0xFC400020) & 0x7;
}

#endif /* __CPU_SH7786_H__ */
