# SPDX-License-Identifier: GPL-2.0
config <PERSON><PERSON><PERSON><PERSON>
	def_bool y
	select ARCH_32BIT_OFF_T
	select ARCH_HAS_CPU_CACHE_ALIASING
	select ARCH_HAVE_NMI_SAFE_CMPXCHG if (GUSA_RB || CPU_SH4A)
	select ARCH_HAS_BINFMT_FLAT if !MMU
	select ARCH_HAS_CPU_FINALIZE_INIT
	select ARCH_HAS_CURRENT_STACK_POINTER
	select ARCH_HAS_GIGANTIC_PAGE
	select ARCH_HAS_GCOV_PROFILE_AL<PERSON>
	select ARCH_HAS_PTE_SPECIAL
	select ARCH_HAS_TICK_BROADCAST if GENERIC_CLOCKEVENTS_BROADCAST
	select <PERSON><PERSON>_HIBERNATION_POSSIBLE if M<PERSON>
	select ARCH_MIGHT_HAVE_PC_PARPORT
	select ARCH_WANT_IPC_PARSE_VERSION
	select CPU_NO_EFFICIENT_FFS
	select DMA_DECLARE_COHERENT
	select GENERIC_ATOM<PERSON><PERSON>
	select GENERIC_CMOS_UPDATE if SH_SH03 || SH_DREAMCAST
	select GENERIC_IDLE_POLL_SETUP
	select GENERIC_IRQ_SHOW
	select GENERIC_LIB_ASHLDI3
	select GENERIC_LIB_ASHRDI3
	select GENERIC_LIB_LSHRDI3
	select GENERIC_PCI_IOMAP if PCI
	select GENERIC_SCHED_CLOCK
	select GENERIC_SMP_IDLE_THREAD
	select GUP_GET_PXX_LOW_HIGH if X2TLB
	select HAS_IOPORT if HAS_IOPORT_MAP
	select GENERIC_IOREMAP if MMU
	select HAVE_ARCH_AUDITSYSCALL
	select HAVE_ARCH_KGDB
	select HAVE_ARCH_SECCOMP_FILTER
	select HAVE_ARCH_TRACEHOOK
	select HAVE_DEBUG_BUGVERBOSE
	select HAVE_DEBUG_KMEMLEAK
	select HAVE_DYNAMIC_FTRACE
	select HAVE_GUP_FAST if MMU
	select HAVE_FUNCTION_GRAPH_TRACER
	select HAVE_FUNCTION_TRACER
	select HAVE_FTRACE_MCOUNT_RECORD
	select HAVE_HW_BREAKPOINT
	select HAVE_IOREMAP_PROT if MMU && !X2TLB
	select HAVE_KERNEL_BZIP2
	select HAVE_KERNEL_GZIP
	select HAVE_KERNEL_LZMA
	select HAVE_KERNEL_LZO
	select HAVE_KERNEL_XZ
	select HAVE_KPROBES
	select HAVE_KRETPROBES
	select HAVE_MIXED_BREAKPOINTS_REGS
	select HAVE_MOD_ARCH_SPECIFIC if DWARF_UNWINDER
	select HAVE_NMI
	select HAVE_PATA_PLATFORM
	select HAVE_PERF_EVENTS
	select HAVE_REGS_AND_STACK_ACCESS_API
	select HAVE_UID16
	select HAVE_SOFTIRQ_ON_OWN_STACK if IRQSTACKS
	select HAVE_STACKPROTECTOR
	select HAVE_SYSCALL_TRACEPOINTS
	select IRQ_FORCED_THREADING
	select LOCK_MM_AND_FIND_VMA
	select MODULES_USE_ELF_RELA
	select NEED_SG_DMA_LENGTH
	select NO_DMA if !MMU && !DMA_COHERENT
	select NO_GENERIC_PCI_IOPORT_MAP if PCI
	select OLD_SIGACTION
	select OLD_SIGSUSPEND
	select PCI_DOMAINS if PCI
	select PERF_EVENTS
	select PERF_USE_VMALLOC
	select RTC_LIB
	select SPARSE_IRQ
	select TRACE_IRQFLAGS_SUPPORT
	help
	  The SuperH is a RISC processor targeted for use in embedded systems
	  and consumer electronics; it was also used in the Sega Dreamcast
	  gaming console.  The SuperH port has a home page at
	  <http://www.linux-sh.org/>.

config GENERIC_BUG
	def_bool y
	depends on BUG

config GENERIC_HWEIGHT
	def_bool y

config GENERIC_CALIBRATE_DELAY
	bool

config GENERIC_LOCKBREAK
	def_bool y
	depends on SMP && PREEMPTION

config ARCH_SUSPEND_POSSIBLE
	def_bool n

config ARCH_HIBERNATION_POSSIBLE
	def_bool n

config SYS_SUPPORTS_APM_EMULATION
	bool
	select ARCH_SUSPEND_POSSIBLE

config SYS_SUPPORTS_SMP
	bool

config SYS_SUPPORTS_NUMA
	bool

config STACKTRACE_SUPPORT
	def_bool y

config LOCKDEP_SUPPORT
	def_bool y

config ARCH_HAS_ILOG2_U32
	def_bool n

config ARCH_HAS_ILOG2_U64
	def_bool n

config NO_IOPORT_MAP
	def_bool !PCI
	depends on !SH_SHMIN && !SH_HP6XX && !SH_SOLUTION_ENGINE && \
		   !SH_DREAMCAST

config IO_TRAPPED
	bool

config SWAP_IO_SPACE
	bool

config DMA_COHERENT
	bool

config DMA_NONCOHERENT
	def_bool !NO_DMA && !DMA_COHERENT
	select ARCH_HAS_DMA_PREP_COHERENT
	select ARCH_HAS_SYNC_DMA_FOR_DEVICE
	select DMA_DIRECT_REMAP

config PGTABLE_LEVELS
	default 3 if X2TLB
	default 2

menu "System type"

#
# Processor families
#
config CPU_SH2
	bool
	select SH_INTC

config CPU_SH2A
	bool
	select CPU_SH2
	select UNCACHED_MAPPING

config CPU_J2
	bool
	select CPU_SH2
	select OF
	select OF_EARLY_FLATTREE

config CPU_SH3
	bool
	select CPU_HAS_INTEVT
	select CPU_HAS_SR_RB
	select SH_INTC
	select SYS_SUPPORTS_SH_TMU

config CPU_SH4
	bool
	select ARCH_SUPPORTS_HUGETLBFS if MMU
	select CPU_HAS_INTEVT
	select CPU_HAS_SR_RB
	select CPU_HAS_FPU if !CPU_SH4AL_DSP
	select SH_INTC
	select SYS_SUPPORTS_SH_TMU

config CPU_SH4A
	bool
	select CPU_SH4

config CPU_SH4AL_DSP
	bool
	select CPU_SH4A
	select CPU_HAS_DSP

config CPU_SHX2
	bool

config CPU_SHX3
	bool
	select DMA_COHERENT
	select SYS_SUPPORTS_SMP
	select SYS_SUPPORTS_NUMA

config ARCH_SHMOBILE
	bool
	select ARCH_SUSPEND_POSSIBLE
	select PM

config CPU_HAS_PMU
       depends on CPU_SH4 || CPU_SH4A
       default y
       bool

choice
	prompt "Processor sub-type selection"

#
# Processor subtypes
#

# SH-2 Processor Support

config CPU_SUBTYPE_SH7619
	bool "Support SH7619 processor"
	select CPU_SH2
	select SYS_SUPPORTS_SH_CMT

config CPU_SUBTYPE_J2
	bool "Support J2 processor"
	select CPU_J2
	select SYS_SUPPORTS_SMP
	select GENERIC_CLOCKEVENTS_BROADCAST if SMP

# SH-2A Processor Support

config CPU_SUBTYPE_SH7201
	bool "Support SH7201 processor"
	select CPU_SH2A
	select CPU_HAS_FPU
	select SYS_SUPPORTS_SH_MTU2
 
config CPU_SUBTYPE_SH7203
	bool "Support SH7203 processor"
	select CPU_SH2A
	select CPU_HAS_FPU
	select SYS_SUPPORTS_SH_CMT
	select SYS_SUPPORTS_SH_MTU2
	select PINCTRL

config CPU_SUBTYPE_SH7206
	bool "Support SH7206 processor"
	select CPU_SH2A
	select SYS_SUPPORTS_SH_CMT
	select SYS_SUPPORTS_SH_MTU2

config CPU_SUBTYPE_SH7263
	bool "Support SH7263 processor"
	select CPU_SH2A
	select CPU_HAS_FPU
	select SYS_SUPPORTS_SH_CMT
	select SYS_SUPPORTS_SH_MTU2

config CPU_SUBTYPE_SH7264
	bool "Support SH7264 processor"
	select CPU_SH2A
	select CPU_HAS_FPU
	select SYS_SUPPORTS_SH_CMT
	select SYS_SUPPORTS_SH_MTU2
	select PINCTRL

config CPU_SUBTYPE_SH7269
	bool "Support SH7269 processor"
	select CPU_SH2A
	select CPU_HAS_FPU
	select SYS_SUPPORTS_SH_CMT
	select SYS_SUPPORTS_SH_MTU2
	select PINCTRL

config CPU_SUBTYPE_MXG
	bool "Support MX-G processor"
	select CPU_SH2A
	select SYS_SUPPORTS_SH_MTU2
	help
	  Select MX-G if running on an R8A03022BG part.

# SH-3 Processor Support

config CPU_SUBTYPE_SH7705
	bool "Support SH7705 processor"
	select CPU_SH3

config CPU_SUBTYPE_SH7706
	bool "Support SH7706 processor"
	select CPU_SH3
	help
	  Select SH7706 if you have a 133 Mhz SH-3 HD6417706 CPU.

config CPU_SUBTYPE_SH7707
	bool "Support SH7707 processor"
	select CPU_SH3
	help
	  Select SH7707 if you have a  60 Mhz SH-3 HD6417707 CPU.

config CPU_SUBTYPE_SH7708
	bool "Support SH7708 processor"
	select CPU_SH3
	help
	  Select SH7708 if you have a  60 Mhz SH-3 HD6417708S or
	  if you have a 100 Mhz SH-3 HD6417708R CPU.

config CPU_SUBTYPE_SH7709
	bool "Support SH7709 processor"
	select CPU_SH3
	help
	  Select SH7709 if you have a  80 Mhz SH-3 HD6417709 CPU.

config CPU_SUBTYPE_SH7710
	bool "Support SH7710 processor"
	select CPU_SH3
	select CPU_HAS_DSP
	help
	  Select SH7710 if you have a SH3-DSP SH7710 CPU.

config CPU_SUBTYPE_SH7712
	bool "Support SH7712 processor"
	select CPU_SH3
	select CPU_HAS_DSP
	help
	  Select SH7712 if you have a SH3-DSP SH7712 CPU.

config CPU_SUBTYPE_SH7720
	bool "Support SH7720 processor"
	select CPU_SH3
	select CPU_HAS_DSP
	select SYS_SUPPORTS_SH_CMT
	select USB_OHCI_SH if USB_OHCI_HCD
	select PINCTRL
	help
	  Select SH7720 if you have a SH3-DSP SH7720 CPU.

config CPU_SUBTYPE_SH7721
	bool "Support SH7721 processor"
	select CPU_SH3
	select CPU_HAS_DSP
	select SYS_SUPPORTS_SH_CMT
	select USB_OHCI_SH if USB_OHCI_HCD
	help
	  Select SH7721 if you have a SH3-DSP SH7721 CPU.

# SH-4 Processor Support

config CPU_SUBTYPE_SH7750
	bool "Support SH7750 processor"
	select CPU_SH4
	help
	  Select SH7750 if you have a 200 Mhz SH-4 HD6417750 CPU.

config CPU_SUBTYPE_SH7091
	bool "Support SH7091 processor"
	select CPU_SH4
	help
	  Select SH7091 if you have an SH-4 based Sega device (such as
	  the Dreamcast, Naomi, and Naomi 2).

config CPU_SUBTYPE_SH7750R
	bool "Support SH7750R processor"
	select CPU_SH4

config CPU_SUBTYPE_SH7750S
	bool "Support SH7750S processor"
	select CPU_SH4

config CPU_SUBTYPE_SH7751
	bool "Support SH7751 processor"
	select CPU_SH4
	help
	  Select SH7751 if you have a 166 Mhz SH-4 HD6417751 CPU,
	  or if you have a HD6417751R CPU.

config CPU_SUBTYPE_SH7751R
	bool "Support SH7751R processor"
	select CPU_SH4

config CPU_SUBTYPE_SH7760
	bool "Support SH7760 processor"
	select CPU_SH4

# SH-4A Processor Support

config CPU_SUBTYPE_SH7723
	bool "Support SH7723 processor"
	select CPU_SH4A
	select CPU_SHX2
	select ARCH_SHMOBILE
	select ARCH_SPARSEMEM_ENABLE
	select SYS_SUPPORTS_SH_CMT
	select PINCTRL
	help
	  Select SH7723 if you have an SH-MobileR2 CPU.

config CPU_SUBTYPE_SH7724
	bool "Support SH7724 processor"
	select CPU_SH4A
	select CPU_SHX2
	select ARCH_SHMOBILE
	select ARCH_SPARSEMEM_ENABLE
	select SYS_SUPPORTS_SH_CMT
	select PINCTRL
	help
	  Select SH7724 if you have an SH-MobileR2R CPU.

config CPU_SUBTYPE_SH7734
	bool "Support SH7734 processor"
	select CPU_SH4A
	select CPU_SHX2
	select PINCTRL
	help
	  Select SH7734 if you have a SH4A SH7734 CPU.

config CPU_SUBTYPE_SH7757
	bool "Support SH7757 processor"
	select CPU_SH4A
	select CPU_SHX2
	select PINCTRL
	help
	  Select SH7757 if you have a SH4A SH7757 CPU.

config CPU_SUBTYPE_SH7763
	bool "Support SH7763 processor"
	select CPU_SH4A
	select USB_OHCI_SH if USB_OHCI_HCD
	help
	  Select SH7763 if you have a SH4A SH7763(R5S77631) CPU.

config CPU_SUBTYPE_SH7770
	bool "Support SH7770 processor"
	select CPU_SH4A

config CPU_SUBTYPE_SH7780
	bool "Support SH7780 processor"
	select CPU_SH4A

config CPU_SUBTYPE_SH7785
	bool "Support SH7785 processor"
	select CPU_SH4A
	select CPU_SHX2
	select ARCH_SPARSEMEM_ENABLE
	select SYS_SUPPORTS_NUMA
	select PINCTRL

config CPU_SUBTYPE_SH7786
	bool "Support SH7786 processor"
	select CPU_SH4A
	select CPU_SHX3
	select CPU_HAS_PTEAEX
	select GENERIC_CLOCKEVENTS_BROADCAST if SMP
	select USB_OHCI_SH if USB_OHCI_HCD
	select USB_EHCI_SH if USB_EHCI_HCD
	select PINCTRL

config CPU_SUBTYPE_SHX3
	bool "Support SH-X3 processor"
	select CPU_SH4A
	select CPU_SHX3
	select GENERIC_CLOCKEVENTS_BROADCAST if SMP
	select GPIOLIB
	select PINCTRL

# SH4AL-DSP Processor Support

config CPU_SUBTYPE_SH7343
	bool "Support SH7343 processor"
	select CPU_SH4AL_DSP
	select ARCH_SHMOBILE
	select SYS_SUPPORTS_SH_CMT

config CPU_SUBTYPE_SH7722
	bool "Support SH7722 processor"
	select CPU_SH4AL_DSP
	select CPU_SHX2
	select ARCH_SHMOBILE
	select ARCH_SPARSEMEM_ENABLE
	select SYS_SUPPORTS_NUMA
	select SYS_SUPPORTS_SH_CMT
	select PINCTRL

config CPU_SUBTYPE_SH7366
	bool "Support SH7366 processor"
	select CPU_SH4AL_DSP
	select CPU_SHX2
	select ARCH_SHMOBILE
	select ARCH_SPARSEMEM_ENABLE
	select SYS_SUPPORTS_NUMA
	select SYS_SUPPORTS_SH_CMT

endchoice

source "arch/sh/mm/Kconfig"
 
source "arch/sh/Kconfig.cpu"

source "arch/sh/boards/Kconfig"

menu "Timer and clock configuration"

config SH_PCLK_FREQ
	int "Peripheral clock frequency (in Hz)"
	depends on SH_CLK_CPG_LEGACY
	default "31250000" if CPU_SUBTYPE_SH7619
	default "33333333" if CPU_SUBTYPE_SH7770 || \
			      CPU_SUBTYPE_SH7760 || \
			      CPU_SUBTYPE_SH7705 || \
			      CPU_SUBTYPE_SH7203 || \
			      CPU_SUBTYPE_SH7206 || \
			      CPU_SUBTYPE_SH7263 || \
			      CPU_SUBTYPE_MXG
	default "60000000" if CPU_SUBTYPE_SH7751 || CPU_SUBTYPE_SH7751R
	default "50000000"
	help
	  This option is used to specify the peripheral clock frequency.
	  This is necessary for determining the reference clock value on
	  platforms lacking an RTC.

config SH_CLK_CPG
	def_bool y

config SH_CLK_CPG_LEGACY
	depends on SH_CLK_CPG
	def_bool y if !CPU_SUBTYPE_SH7785 && !ARCH_SHMOBILE && \
		      !CPU_SHX3 && !CPU_SUBTYPE_SH7757 && \
		      !CPU_SUBTYPE_SH7734 && !CPU_SUBTYPE_SH7264 && \
		      !CPU_SUBTYPE_SH7269

endmenu

menu "CPU Frequency scaling"
source "drivers/cpufreq/Kconfig"
endmenu

source "arch/sh/drivers/Kconfig"

endmenu

menu "Kernel features"

source "kernel/Kconfig.hz"

config ARCH_SUPPORTS_KEXEC
	def_bool MMU

config ARCH_SUPPORTS_CRASH_DUMP
	def_bool BROKEN_ON_SMP

config ARCH_DEFAULT_CRASH_DUMP
	def_bool y

config ARCH_SUPPORTS_KEXEC_JUMP
	def_bool y

config PHYSICAL_START
	hex "Physical address where the kernel is loaded" if (EXPERT || CRASH_DUMP)
	default MEMORY_START
	help
	  This gives the physical address where the kernel is loaded
	  and is ordinarily the same as MEMORY_START.

	  Different values are primarily used in the case of kexec on panic
	  where the fail safe kernel needs to run at a different address
	  than the panic-ed kernel.

config SMP
	bool "Symmetric multi-processing support"
	depends on SYS_SUPPORTS_SMP
	help
	  This enables support for systems with more than one CPU. If you have
	  a system with only one CPU, say N. If you have a system with more
	  than one CPU, say Y.

	  If you say N here, the kernel will run on uni- and multiprocessor
	  machines, but will use only one CPU of a multiprocessor machine. If
	  you say Y here, the kernel will run on many, but not all,
	  uniprocessor machines. On a uniprocessor machine, the kernel
	  will run faster if you say N here.

	  People using multiprocessor machines who say Y here should also say
	  Y to "Enhanced Real Time Clock Support", below.

	  See also <file:Documentation/admin-guide/lockup-watchdogs.rst> and the SMP-HOWTO
	  available at <https://www.tldp.org/docs.html#howto>.

	  If you don't know what to do here, say N.

config NR_CPUS
	int "Maximum number of CPUs (2-32)"
	range 2 32
	depends on SMP
	default "4" if CPU_SUBTYPE_SHX3
	default "2"
	help
	  This allows you to specify the maximum number of CPUs which this
	  kernel will support.  The maximum supported value is 32 and the
	  minimum value which makes sense is 2.

	  This is purely to save memory - each supported CPU adds
	  approximately eight kilobytes to the kernel image.

config HOTPLUG_CPU
	bool "Support for hot-pluggable CPUs (EXPERIMENTAL)"
	depends on SMP
	help
	  Say Y here to experiment with turning CPUs off and on.  CPUs
	  can be controlled through /sys/devices/system/cpu.

config GUSA
	def_bool y
	depends on !SMP
	help
	  This enables support for gUSA (general UserSpace Atomicity).
	  This is the default implementation for both UP and non-ll/sc
	  CPUs, and is used by the libc, amongst others.

	  For additional information, design information can be found 
	  in <http://lc.linux.or.jp/lc2002/papers/niibe0919p.pdf>.

	  This should only be disabled for special cases where alternate
	  atomicity implementations exist.

config GUSA_RB
	bool "Implement atomic operations by roll-back (gRB) (EXPERIMENTAL)"
	depends on GUSA && CPU_SH3 || (CPU_SH4 && !CPU_SH4A)
	help
	  Enabling this option will allow the kernel to implement some
	  atomic operations using a software implementation of load-locked/
	  store-conditional (LLSC). On machines which do not have hardware
	  LLSC, this should be more efficient than the other alternative of
	  disabling interrupts around the atomic sequence.

config HW_PERF_EVENTS
	bool "Enable hardware performance counter support for perf events"
	depends on PERF_EVENTS && CPU_HAS_PMU
	default y
	help
	  Enable hardware performance counter support for perf events. If
	  disabled, perf events will use software events only.

source "drivers/sh/Kconfig"

endmenu

menu "Boot options"

config USE_BUILTIN_DTB
	bool "Use builtin DTB"
	default n
	depends on SH_DEVICE_TREE
	help
	  Link a device tree blob for particular hardware into the kernel,
	  suppressing use of the DTB pointer provided by the bootloader.
	  This option should only be used with legacy bootloaders that are
	  not capable of providing a DTB to the kernel, or for experimental
	  hardware without stable device tree bindings.

config BUILTIN_DTB_SOURCE
	string "Source file for builtin DTB"
	default ""
	depends on USE_BUILTIN_DTB
	help
	  Base name (without suffix, relative to arch/sh/boot/dts) for the
	  a DTS file that will be used to produce the DTB linked into the
	  kernel.

config ZERO_PAGE_OFFSET
	hex
	default "0x00010000" if PAGE_SIZE_64KB || SH_RTS7751R2D || \
				SH_7751_SOLUTION_ENGINE
	default "0x00004000" if PAGE_SIZE_16KB || SH_SH03
	default "0x00002000" if PAGE_SIZE_8KB
	default "0x00001000"
	help
	  This sets the default offset of zero page.

config BOOT_LINK_OFFSET
	hex
	default "0x00210000" if SH_SHMIN
	default "0x00810000" if SH_7780_SOLUTION_ENGINE
	default "0x009e0000" if SH_TITAN
	default "0x01800000" if SH_SDK7780
	default "0x02000000" if SH_EDOSK7760
	default "0x00800000"
	help
	  This option allows you to set the link address offset of the zImage.
	  This can be useful if you are on a board which has a small amount of
	  memory.

config ENTRY_OFFSET
	hex
	default "0x00001000" if PAGE_SIZE_4KB
	default "0x00002000" if PAGE_SIZE_8KB
	default "0x00004000" if PAGE_SIZE_16KB
	default "0x00010000" if PAGE_SIZE_64KB
	default "0x00000000"

config ROMIMAGE_MMCIF
	bool "Include MMCIF loader in romImage (EXPERIMENTAL)"
	depends on CPU_SUBTYPE_SH7724
	help
	  Say Y here to include experimental MMCIF loading code in
	  romImage. With this enabled it is possible to write the romImage
	  kernel image to an MMC card and boot the kernel straight from
	  the reset vector. At reset the processor Mask ROM will load the
	  first part of the romImage which in turn loads the rest the kernel
	  image to RAM using the MMCIF hardware block.

choice
	prompt "Kernel command line"
	default CMDLINE_OVERWRITE
	help
	  Setting this option allows the kernel command line arguments
	  to be set.

config CMDLINE_OVERWRITE
	bool "Overwrite bootloader kernel arguments"
	help
	  Given string will overwrite any arguments passed in by
	  a bootloader.

config CMDLINE_EXTEND
	bool "Extend bootloader kernel arguments"
	help
	  Given string will be concatenated with arguments passed in
	  by a bootloader.

config CMDLINE_FROM_BOOTLOADER
	bool "Use bootloader kernel arguments"
	help
	  Uses the command-line options passed by the boot loader.

endchoice

config CMDLINE
	string "Kernel command line arguments string"
	depends on CMDLINE_OVERWRITE || CMDLINE_EXTEND
	default "console=ttySC1,115200"

endmenu

menu "Bus options"

config MAPLE
	bool "Maple Bus support"
	depends on SH_DREAMCAST
	help
	 The Maple Bus is SEGA's serial communication bus for peripherals
	 on the Dreamcast. Without this bus support you won't be able to
	 get your Dreamcast keyboard etc to work, so most users
	 probably want to say 'Y' here, unless you are only using the
	 Dreamcast with a serial line terminal or a remote network
	 connection.

endmenu

menu "Power management options (EXPERIMENTAL)"

source "kernel/power/Kconfig"

source "drivers/cpuidle/Kconfig"

endmenu
