# SPDX-License-Identifier: GPL-2.0
#
# Specific board support, not covered by a mach group.
#
obj-$(CONFIG_SH_MAGIC_PANEL_R2)	+= board-magicpanelr2.o
obj-$(CONFIG_SH_SECUREEDGE5410)	+= board-secureedge5410.o
obj-$(CONFIG_SH_SH2007)		+= board-sh2007.o
obj-$(CONFIG_SH_SH7785LCR)	+= board-sh7785lcr.o
obj-$(CONFIG_SH_URQUELL)	+= board-urquell.o
obj-$(CONFIG_SH_SHMIN)		+= board-shmin.o
obj-$(CONFIG_SH_EDOSK7705)	+= board-edosk7705.o
obj-$(CONFIG_SH_EDOSK7760)	+= board-edosk7760.o
obj-$(CONFIG_SH_ESPT)		+= board-espt.o
obj-$(CONFIG_SH_POLARIS)	+= board-polaris.o
obj-$(CONFIG_SH_TITAN)		+= board-titan.o
obj-$(CONFIG_SH_SH7757LCR)	+= board-sh7757lcr.o
obj-$(CONFIG_SH_APSH4A3A)	+= board-apsh4a3a.o
obj-$(CONFIG_SH_APSH4AD0A)	+= board-apsh4ad0a.o

obj-$(CONFIG_SH_DEVICE_TREE)	+= of-generic.o

obj-$(CONFIG_SOLUTION_ENGINE)	+= mach-se/
obj-$(CONFIG_SH_HP6XX)		+= mach-hp6xx/
obj-$(CONFIG_SH_DREAMCAST)	+= mach-dreamcast/
obj-$(CONFIG_SH_SH03)		+= mach-sh03/
obj-$(CONFIG_SH_RTS7751R2D)	+= mach-r2d/
obj-$(CONFIG_SH_HIGHLANDER)	+= mach-highlander/
obj-$(CONFIG_SH_MIGOR)		+= mach-migor/
obj-$(CONFIG_SH_AP325RXA)	+= mach-ap325rxa/
obj-$(CONFIG_SH_KFR2R09)	+= mach-kfr2r09/
obj-$(CONFIG_SH_ECOVEC)		+= mach-ecovec24/
obj-$(CONFIG_SH_SDK7780)	+= mach-sdk7780/
obj-$(CONFIG_SH_SDK7786)	+= mach-sdk7786/
obj-$(CONFIG_SH_X3PROTO)	+= mach-x3proto/
obj-$(CONFIG_SH_SH7763RDP)	+= mach-sh7763rdp/
obj-$(CONFIG_SH_LANDISK)	+= mach-landisk/
obj-$(CONFIG_SH_LBOX_RE2)	+= mach-lboxre2/
obj-$(CONFIG_SH_RSK)		+= mach-rsk/
