# SPDX-License-Identifier: GPL-2.0
menu "Board support"

config SOLUTION_ENGINE
	bool

config SH_ALPHA_BOARD
	bool

config SH_CUSTOM_CLK
	def_bool y
	depends on !SH_DEVICE_TREE
	select HAVE_LEGACY_CLK

config SH_DEVICE_TREE
	bool
	select OF
	select OF_EARLY_FLATTREE
	select TIMER_OF
	select COMMON_<PERSON><PERSON><PERSON>
	select GENERIC_CALIBRATE_DELAY

config SH_JCORE_SOC
	bool "J-Core SoC"
	select SH_DEVICE_TREE
	select CLKSRC_JCORE_PIT
	select JCORE_AIC
	depends on CPU_J2
	help
	  Select this option to include drivers core components of the
	  J-Core SoC, including interrupt controllers and timers.

config SH_SOLUTION_ENGINE
	bool "SolutionEngine"
	select SOLUTION_ENGINE
	select CPU_HAS_IPR_IRQ
	depends on CPU_SUBTYPE_SH7705 || CPU_SUBTYPE_SH7709 || CPU_SUBTYPE_SH7710 || \
	  CPU_SUBTYPE_SH7712 || CPU_SUBTYPE_SH7750 || CPU_SUBTYPE_SH7750S || \
	  CPU_SUBTYPE_SH7750R 
	help
	  Select SolutionEngine if configuring for a Hitachi SH7705, SH7709,
	  SH7710, SH7712, SH7750, SH7750S or SH7750R evaluation board.

config SH_7206_SOLUTION_ENGINE
	bool "SolutionEngine7206"
	select SOLUTION_ENGINE
	depends on CPU_SUBTYPE_SH7206
	help
	  Select 7206 SolutionEngine if configuring for a Hitachi SH7206
	  evaluation board.

config SH_7619_SOLUTION_ENGINE
	bool "SolutionEngine7619"
	select SOLUTION_ENGINE
	depends on CPU_SUBTYPE_SH7619
	help
	  Select 7619 SolutionEngine if configuring for a Hitachi SH7619
	  evaluation board.
	
config SH_7721_SOLUTION_ENGINE
	bool "SolutionEngine7721"
	select SOLUTION_ENGINE
	depends on CPU_SUBTYPE_SH7721
	help
	  Select 7721 SolutionEngine if configuring for a Hitachi SH7721
	  evaluation board.

config SH_7722_SOLUTION_ENGINE
	bool "SolutionEngine7722"
	select SOLUTION_ENGINE
	select GENERIC_IRQ_CHIP
	select IRQ_DOMAIN
	depends on CPU_SUBTYPE_SH7722
	help
	  Select 7722 SolutionEngine if configuring for a Hitachi SH772
	  evaluation board.

config SH_7724_SOLUTION_ENGINE
	bool "SolutionEngine7724"
	select SOLUTION_ENGINE
	depends on CPU_SUBTYPE_SH7724
	select GPIOLIB
	select SND_SOC_AK4642 if SND_SIMPLE_CARD
	select REGULATOR_FIXED_VOLTAGE if REGULATOR
	help
	  Select 7724 SolutionEngine if configuring for a Hitachi SH7724
	  evaluation board.

config SH_7751_SOLUTION_ENGINE
	bool "SolutionEngine7751"
	select SOLUTION_ENGINE
	select CPU_HAS_IPR_IRQ
	depends on CPU_SUBTYPE_SH7751
	help
	  Select 7751 SolutionEngine if configuring for a Hitachi SH7751
	  evaluation board.
	  
config SH_7780_SOLUTION_ENGINE
	bool "SolutionEngine7780"
	select SOLUTION_ENGINE
	select HAVE_PCI
	depends on CPU_SUBTYPE_SH7780
	help
	  Select 7780 SolutionEngine if configuring for a Renesas SH7780
	  evaluation board.

config SH_7343_SOLUTION_ENGINE
	bool "SolutionEngine7343"
	select SOLUTION_ENGINE
	select GENERIC_IRQ_CHIP
	select IRQ_DOMAIN
	depends on CPU_SUBTYPE_SH7343
	help
	  Select 7343 SolutionEngine if configuring for a Hitachi
	  SH7343 (SH-Mobile 3AS) evaluation board.

config SH_HP6XX
	bool "HP6XX"
	select SYS_SUPPORTS_APM_EMULATION
	select HD6446X_SERIES
	depends on CPU_SUBTYPE_SH7709
	help
	  Select HP6XX if configuring for a HP jornada HP6xx.
	  More information (hardware only) at
	  <http://www.hp.com/jornada/>.

config SH_DREAMCAST
	bool "Dreamcast"
	select HAVE_PCI
	depends on CPU_SUBTYPE_SH7091
	help
	  Select Dreamcast if configuring for a SEGA Dreamcast.
	  More information at <http://www.linux-sh.org>

config SH_SH03
	bool "Interface CTP/PCI-SH03"
	depends on CPU_SUBTYPE_SH7751
	select CPU_HAS_IPR_IRQ
	select HAVE_PCI
	help
	  CTP/PCI-SH03 is a CPU module computer that is produced
	  by Interface Corporation.
	  More information at <http://www.interface.co.jp>

config SH_SECUREEDGE5410
	bool "SecureEdge5410"
	depends on CPU_SUBTYPE_SH7751R
	select CPU_HAS_IPR_IRQ
	select HAVE_PCI
	help
	  Select SecureEdge5410 if configuring for a SnapGear SH board.
	  This includes both the OEM SecureEdge products as well as the
	  SME product line.

config SH_RTS7751R2D
	bool "RTS7751R2D"
	depends on CPU_SUBTYPE_SH7751R
	select HAVE_PCI
	select IO_TRAPPED if MMU
	help
	  Select RTS7751R2D if configuring for a Renesas Technology
	  Sales SH-Graphics board.

config SH_RSK
	bool "Renesas Starter Kit"
	depends on CPU_SUBTYPE_SH7201 || CPU_SUBTYPE_SH7203 || \
	  CPU_SUBTYPE_SH7264 || CPU_SUBTYPE_SH7269
	select REGULATOR_FIXED_VOLTAGE if REGULATOR
	help
	 Select this option if configuring for any of the RSK+ MCU
	 evaluation platforms.

config SH_SDK7780
	bool "SDK7780R3"
	depends on CPU_SUBTYPE_SH7780
	select HAVE_PCI
	help
	  Select SDK7780 if configuring for a Renesas SH7780 SDK7780R3
	  evaluation board.

config SH_SDK7786
	bool "SDK7786"
	depends on CPU_SUBTYPE_SH7786
	select HAVE_PCI
	select NO_IOPORT_MAP if !PCI
	select HAVE_SRAM_POOL
	select REGULATOR_FIXED_VOLTAGE if REGULATOR
	help
	  Select SDK7786 if configuring for a Renesas Technology Europe
	  SH7786-65nm board.

config SH_HIGHLANDER
	bool "Highlander"
	depends on CPU_SUBTYPE_SH7780 || CPU_SUBTYPE_SH7785
	select HAVE_PCI
	select IO_TRAPPED if MMU

config SH_SH7757LCR
	bool "SH7757LCR"
	depends on CPU_SUBTYPE_SH7757
	select GPIOLIB
	select REGULATOR_FIXED_VOLTAGE if REGULATOR

config SH_SH7785LCR
	bool "SH7785LCR"
	depends on CPU_SUBTYPE_SH7785
	select HAVE_PCI

config SH_SH7785LCR_29BIT_PHYSMAPS
	bool "SH7785LCR 29bit physmaps"
	depends on SH_SH7785LCR && 29BIT
	default y
	help
	  This board has 2 physical memory maps. It can be changed with
	  DIP switch(S2-5). If you set the DIP switch for S2-5 = ON,
	  you can access all on-board device in 29bit address mode.

config SH_SH7785LCR_PT
	bool "SH7785LCR prototype board on 32-bit MMU mode"
	depends on SH_SH7785LCR && 32BIT
	default n
	help
	  If you use prototype board, this option is enabled.

config SH_URQUELL
	bool "Urquell"
	depends on CPU_SUBTYPE_SH7786
	select GPIOLIB
	select HAVE_PCI
	select NO_IOPORT_MAP if !PCI

config SH_MIGOR
	bool "Migo-R"
	depends on CPU_SUBTYPE_SH7722
	select GPIOLIB
	select REGULATOR_FIXED_VOLTAGE if REGULATOR
	help
	  Select Migo-R if configuring for the SH7722 Migo-R platform
          by Renesas System Solutions Asia Pte. Ltd.

config SH_AP325RXA
	bool "AP-325RXA"
	depends on CPU_SUBTYPE_SH7723
	select GPIOLIB
	select REGULATOR_FIXED_VOLTAGE if REGULATOR
	help
	  Renesas "AP-325RXA" support.
	  Compatible with ALGO SYSTEM CO.,LTD. "AP-320A"

config SH_KFR2R09
	bool "KFR2R09"
	depends on CPU_SUBTYPE_SH7724
	select GPIOLIB
	select REGULATOR_FIXED_VOLTAGE if REGULATOR
	help
	  "Kit For R2R for 2009" support.

config SH_ECOVEC
	bool "EcoVec"
	depends on CPU_SUBTYPE_SH7724
	select GPIOLIB
	select SND_SOC_DA7210 if SND_SIMPLE_CARD
	select REGULATOR_FIXED_VOLTAGE if REGULATOR
	help
	  Renesas "R0P7724LC0011/21RL (EcoVec)" support.

config SH_SH7763RDP
	bool "SH7763RDP"
	depends on CPU_SUBTYPE_SH7763
	help
	  Select SH7763RDP if configuring for a Renesas SH7763
	  evaluation board.

config SH_ESPT
	bool "ESPT"
	depends on CPU_SUBTYPE_SH7763
	help
	  Select ESPT if configuring for a Renesas SH7763
	  with gigabit ether evaluation board.

config SH_EDOSK7705
	bool "EDOSK7705"
	depends on CPU_SUBTYPE_SH7705

config SH_EDOSK7760
	bool "EDOSK7760"
	depends on CPU_SUBTYPE_SH7760
	help
	  Select if configuring for a Renesas EDOSK7760
	  evaluation board.

config SH_LANDISK
	bool "LANDISK"
	depends on CPU_SUBTYPE_SH7751R
	select HAVE_PCI
	help
	  I-O DATA DEVICE, INC. "LANDISK Series" support.

config SH_TITAN
	bool "TITAN"
	depends on CPU_SUBTYPE_SH7751R
	select CPU_HAS_IPR_IRQ
	select HAVE_PCI
	help
	  Select Titan if you are configuring for a Nimble Microsystems
	  NetEngine NP51R.

config SH_SHMIN
	bool "SHMIN"
	depends on CPU_SUBTYPE_SH7706
	select CPU_HAS_IPR_IRQ
	help
	  Select SHMIN if configuring for the SHMIN board.

config SH_LBOX_RE2
	bool "L-BOX RE2"
	depends on CPU_SUBTYPE_SH7751R
	select HAVE_PCI
	help
	  Select L-BOX RE2 if configuring for the NTT COMWARE L-BOX RE2.

config SH_X3PROTO
	bool "SH-X3 Prototype board"
	depends on CPU_SUBTYPE_SHX3
	select NO_IOPORT_MAP if !PCI
	select IRQ_DOMAIN

config SH_MAGIC_PANEL_R2
	bool "Magic Panel R2"
	depends on CPU_SUBTYPE_SH7720
	select GPIOLIB
	select REGULATOR_FIXED_VOLTAGE if REGULATOR
	help
	  Select Magic Panel R2 if configuring for Magic Panel R2.

config SH_POLARIS
	bool "SMSC Polaris"
	select CPU_HAS_IPR_IRQ
	select REGULATOR_FIXED_VOLTAGE if REGULATOR
	depends on CPU_SUBTYPE_SH7709
	help
	  Select if configuring for an SMSC Polaris development board

config SH_SH2007
	bool "SH-2007 board"
	select NO_IOPORT_MAP
	select REGULATOR_FIXED_VOLTAGE if REGULATOR
	depends on CPU_SUBTYPE_SH7780
	help
	  SH-2007 is a single-board computer based around SH7780 chip
	  intended for embedded applications.
	  It has an Ethernet interface (SMC9118), direct connected
	  Compact Flash socket, two serial ports and PC-104 bus.

config SH_APSH4A3A
	bool "AP-SH4A-3A"
	select SH_ALPHA_BOARD
	select REGULATOR_FIXED_VOLTAGE if REGULATOR
	depends on CPU_SUBTYPE_SH7785
	help
	  Select AP-SH4A-3A if configuring for an ALPHAPROJECT AP-SH4A-3A.

config SH_APSH4AD0A
	bool "AP-SH4AD-0A"
	select SH_ALPHA_BOARD
	select HAVE_PCI
	select REGULATOR_FIXED_VOLTAGE if REGULATOR
	depends on CPU_SUBTYPE_SH7786
	help
	  Select AP-SH4AD-0A if configuring for an ALPHAPROJECT AP-SH4AD-0A.

source "arch/sh/boards/mach-r2d/Kconfig"
source "arch/sh/boards/mach-highlander/Kconfig"
source "arch/sh/boards/mach-sdk7780/Kconfig"
source "arch/sh/boards/mach-migor/Kconfig"
source "arch/sh/boards/mach-rsk/Kconfig"

if SH_MAGIC_PANEL_R2

menu "Magic Panel R2 options"

config SH_MAGIC_PANEL_R2_VERSION
	int "Magic Panel R2 Version"
	default "3"
	help
	  Set the version of the Magic Panel R2

endmenu

endif

endmenu
