# SPDX-License-Identifier: GPL-2.0
menu "Companion Chips"

config HD6446X_SERIES
	bool

choice
	prompt "HD6446x options"
	depends on HD6446X_SERIES
	default HD64461

config HD64461
	bool "Hitachi HD64461 companion chip support"
	help
	  The Hitachi HD64461 provides an interface for
	  the SH7709 CPU, supporting a LCD controller,
	  CRT color controller, IrDA up to 4 Mbps, and a
	  PCMCIA controller supporting 2 slots.

	  More information is available at
	  <http://semiconductor.hitachi.com/windowsce/superh/sld013.htm>.

	  Say Y if you want support for the HD64461.
	  Otherwise, say <PERSON>.

endchoice

# These will also be split into the Kconfig's below
config HD64461_IRQ
	int "HD64461 IRQ"
	depends on HD64461
	default "52"
	help
	  The default setting of the HD64461 IRQ is 52.

	  Do not change this unless you know what you are doing.

config HD64461_ENABLER
	bool "HD64461 PCMCIA enabler"
	depends on HD64461
	help
	  Say Y here if you want to enable PCMCIA support
	  via the HD64461 companion chip.
	  Otherwise, say <PERSON><PERSON>

endmenu
