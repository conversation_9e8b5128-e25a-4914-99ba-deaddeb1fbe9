# SPDX-License-Identifier: GPL-2.0

menu "Accelerated Cryptographic Algorithms for CPU (sparc64)"

config CRYPTO_DES_SPARC64
	tristate "Ciphers: DES and Triple DES EDE, modes: ECB/CBC"
	depends on SPARC64
	select CRYP<PERSON>_<PERSON><PERSON><PERSON>
	select CR<PERSON><PERSON>_<PERSON>IB_DES
	select CRYP<PERSON>_SKCIPHER
	help
	  Block cipher: DES (FIPS 46-2) cipher algorithm
	  Block cipher: Triple DES EDE (FIPS 46-3) cipher algorithm
	  Length-preserving ciphers: DES with ECB and CBC modes
	  Length-preserving ciphers: Tripe DES EDE with ECB and CBC modes

	  Architecture: sparc64

config CRYPTO_CRC32C_SPARC64
	tristate "CRC32c"
	depends on SPARC64
	select CRYPTO_HASH
	select CRC32
	help
	  CRC32c CRC algorithm with the iSCSI polynomial (RFC 3385 and RFC 3720)

	  Architecture: sparc64

config CRYPTO_MD5_SPARC64
	tristate "Digests: MD5"
	depends on SPARC64
	select CRYPTO_MD5
	select CRYPTO_HASH
	help
	  MD5 message digest algorithm (RFC1321)

	  Architecture: sparc64 using crypto instructions, when available

config CRYPTO_SHA1_<PERSON>ARC64
	tristate "Hash functions: SHA-1"
	depends on SPARC64
	select CRYPTO_SHA1
	select CRYPTO_HASH
	help
	  SHA-1 secure hash algorithm (FIPS 180)

	  Architecture: sparc64

config CRYPTO_SHA256_SPARC64
	tristate "Hash functions: SHA-224 and SHA-256"
	depends on SPARC64
	select CRYPTO_SHA256
	select CRYPTO_HASH
	help
	  SHA-224 and SHA-256 secure hash algorithms (FIPS 180)

	  Architecture: sparc64 using crypto instructions, when available

config CRYPTO_SHA512_SPARC64
	tristate "Hash functions: SHA-384 and SHA-512"
	depends on SPARC64
	select CRYPTO_SHA512
	select CRYPTO_HASH
	help
	  SHA-384 and SHA-512 secure hash algorithms (FIPS 180)

	  Architecture: sparc64 using crypto instructions, when available

config CRYPTO_AES_SPARC64
	tristate "Ciphers: AES, modes: ECB, CBC, CTR"
	depends on SPARC64
	select CRYPTO_SKCIPHER
	help
	  Block ciphers: AES cipher algorithms (FIPS-197)
	  Length-preseving ciphers: AES with ECB, CBC, and CTR modes

	  Architecture: sparc64 using crypto instructions

config CRYPTO_CAMELLIA_SPARC64
	tristate "Ciphers: Camellia, modes: ECB, CBC"
	depends on SPARC64
	select CRYPTO_ALGAPI
	select CRYPTO_SKCIPHER
	help
	  Block ciphers: Camellia cipher algorithms
	  Length-preserving ciphers: Camellia with ECB and CBC modes

	  Architecture: sparc64

endmenu
