/* SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note */
#ifndef _ASM_S390_PERF_REGS_H
#define _ASM_S390_PERF_REGS_H

enum perf_event_s390_regs {
	PERF_REG_S390_R0,
	PERF_REG_S390_R1,
	PERF_REG_S390_R2,
	PERF_REG_S390_R3,
	PERF_REG_S390_R4,
	PERF_REG_S390_R5,
	PERF_REG_S390_R6,
	PERF_REG_S390_R7,
	PERF_REG_S390_R8,
	PERF_REG_S390_R9,
	PERF_REG_S390_R10,
	PERF_REG_S390_R11,
	PERF_REG_S390_R12,
	PERF_REG_S390_R13,
	PERF_REG_S390_R14,
	PERF_REG_S390_R15,
	PERF_REG_S390_FP0,
	PERF_REG_S390_FP1,
	PERF_REG_S390_FP2,
	PERF_REG_S390_FP3,
	PERF_REG_S390_FP4,
	PERF_REG_S390_FP5,
	PERF_REG_S390_FP6,
	PERF_REG_S390_FP7,
	PERF_REG_S390_FP8,
	PERF_REG_S390_FP9,
	PERF_REG_S390_FP10,
	PERF_REG_S390_FP11,
	PERF_REG_S390_FP12,
	PERF_REG_S390_FP13,
	PERF_REG_S390_FP14,
	PERF_REG_S390_FP15,
	PERF_REG_S390_MASK,
	PERF_REG_S390_PC,

	PERF_REG_S390_MAX
};

#endif /* _ASM_S390_PERF_REGS_H */
