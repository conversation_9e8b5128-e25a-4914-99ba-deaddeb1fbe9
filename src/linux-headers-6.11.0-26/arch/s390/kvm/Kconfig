# SPDX-License-Identifier: GPL-2.0
#
# KVM configuration
#
source "virt/kvm/Kconfig"

menuconfig VIRTUALIZATION
	def_bool y
	prompt "KVM"
	help
	  Say Y here to get to see options for using your Linux host to run other
	  operating systems inside virtual machines (guests).
	  This option alone does not add any kernel code.

	  If you say N, all options in this submenu will be skipped and disabled.

if VIRTUALIZATION

config KVM
	def_tristate y
	prompt "Kernel-based Virtual Machine (KVM) support"
	select HAVE_KVM_CPU_RELAX_INTERCEPT
	select HAVE_KVM_VCPU_ASYNC_IOCTL
	select KVM_ASYNC_PF
	select KVM_ASYNC_PF_SYNC
	select KVM_COMMON
	select HAVE_KVM_IRQCHIP
	select HAVE_KVM_IRQ_ROUTING
	select HAVE_KVM_INVALID_WAKEUPS
	select HAVE_KVM_NO_POLL
	select KVM_VFIO
	select MMU_NOTIFIER
	help
	  Support hosting paravirtualized guest machines using the SIE
	  virtualization capability on the mainframe. This should work
	  on any 64bit machine.

	  This module provides access to the hardware capabilities through
	  a character device node named /dev/kvm.

	  To compile this as a module, choose M here: the module
	  will be called kvm.

	  If unsure, say N.

config KVM_S390_UCONTROL
	bool "Userspace controlled virtual machines"
	depends on KVM
	help
	  Allow CAP_SYS_ADMIN users to create KVM virtual machines that are
	  controlled by userspace.

	  If unsure, say N.

endif # VIRTUALIZATION
