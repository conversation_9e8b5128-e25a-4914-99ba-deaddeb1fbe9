# SPDX-License-Identifier: GPL-2.0

config EARLY_PRINTK
	def_bool y

config DEBUG_ENTRY
	bool "Debug low-level entry code"
	depends on DEBUG_KERNEL
	help
	  This option enables sanity checks in s390 low-level entry code.
	  Some of these sanity checks may slow down kernel entries and
	  exits or otherwise impact performance.

	  If unsure, say N.

config CIO_INJECT
	bool "CIO Inject interfaces"
	depends on DEBUG_KERNEL && DEBUG_FS
	help
	  This option provides a debugging facility to inject certain artificial events
	  and instruction responses to the CIO layer of Linux kernel. The newly created
	  debugfs user-interfaces will be at /sys/kernel/debug/s390/cio/*
